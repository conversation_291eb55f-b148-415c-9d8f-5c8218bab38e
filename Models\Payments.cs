namespace SmartBooks.Models
{
    public class CustomerPayment
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }
        public decimal Amount { get; set; }
        public DateTime PaymentDate { get; set; } = DateTime.Now;
        public string PaymentMethod { get; set; } = "نقدي";
        public int? CashBoxId { get; set; }
        public string Notes { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public int? UserId { get; set; }

        // Navigation properties
        public Customer? Customer { get; set; }
        public CashBox? CashBox { get; set; }
        public User? User { get; set; }
    }

    public class SupplierPayment
    {
        public int Id { get; set; }
        public int SupplierId { get; set; }
        public decimal Amount { get; set; }
        public DateTime PaymentDate { get; set; } = DateTime.Now;
        public string PaymentMethod { get; set; } = "نقدي";
        public int? CashBoxId { get; set; }
        public string Notes { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public int? UserId { get; set; }

        // Navigation properties
        public Supplier? Supplier { get; set; }
        public CashBox? CashBox { get; set; }
        public User? User { get; set; }
    }
}
