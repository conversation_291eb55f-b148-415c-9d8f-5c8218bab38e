<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>SmartBooks - نظام إدارة المبيعات والمشتريات</AssemblyTitle>
    <AssemblyDescription>نظام متكامل لإدارة المبيعات والمشتريات والمخزون والشؤون المالية</AssemblyDescription>
    <AssemblyCompany>SmartBooks</AssemblyCompany>
    <AssemblyProduct>SmartBooks</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Data.SQLite" Version="1.0.118" />
    <PackageReference Include="ZXing.Net" Version="0.16.9" />
    <PackageReference Include="ZXing.Net.Bindings.Windows.Compatibility" Version="0.16.12" />
    <PackageReference Include="iTextSharp" Version="********" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Forms\" />
    <Folder Include="Models\" />
    <Folder Include="DAL\" />
    <Folder Include="Reports\" />
    <Folder Include="Printing\" />
    <Folder Include="Utilities\" />
    <Folder Include="Resources\" />
  </ItemGroup>

</Project>
