namespace SmartBooks.Forms
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            menuStrip1 = new MenuStrip();
            fileToolStripMenuItem = new ToolStripMenuItem();
            logoutToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator1 = new ToolStripSeparator();
            exitToolStripMenuItem = new ToolStripMenuItem();
            dataToolStripMenuItem = new ToolStripMenuItem();
            customersToolStripMenuItem = new ToolStripMenuItem();
            suppliersToolStripMenuItem = new ToolStripMenuItem();
            productsToolStripMenuItem = new ToolStripMenuItem();
            categoriesToolStripMenuItem = new ToolStripMenuItem();
            salesToolStripMenuItem = new ToolStripMenuItem();
            salesInvoiceToolStripMenuItem = new ToolStripMenuItem();
            salesReturnsToolStripMenuItem = new ToolStripMenuItem();
            purchasesToolStripMenuItem = new ToolStripMenuItem();
            purchaseInvoiceToolStripMenuItem = new ToolStripMenuItem();
            purchaseReturnsToolStripMenuItem = new ToolStripMenuItem();
            inventoryToolStripMenuItem = new ToolStripMenuItem();
            inventoryMovementsToolStripMenuItem = new ToolStripMenuItem();
            stockAdjustmentToolStripMenuItem = new ToolStripMenuItem();
            financialToolStripMenuItem = new ToolStripMenuItem();
            cashBoxesToolStripMenuItem = new ToolStripMenuItem();
            expensesToolStripMenuItem = new ToolStripMenuItem();
            customerPaymentsToolStripMenuItem = new ToolStripMenuItem();
            supplierPaymentsToolStripMenuItem = new ToolStripMenuItem();
            reportsToolStripMenuItem = new ToolStripMenuItem();
            salesReportsToolStripMenuItem = new ToolStripMenuItem();
            purchaseReportsToolStripMenuItem = new ToolStripMenuItem();
            inventoryReportsToolStripMenuItem = new ToolStripMenuItem();
            financialReportsToolStripMenuItem = new ToolStripMenuItem();
            settingsToolStripMenuItem = new ToolStripMenuItem();
            usersToolStripMenuItem = new ToolStripMenuItem();
            backupToolStripMenuItem = new ToolStripMenuItem();
            companySettingsToolStripMenuItem = new ToolStripMenuItem();
            toolStrip1 = new ToolStrip();
            btnCustomers = new ToolStripButton();
            btnSuppliers = new ToolStripButton();
            btnProducts = new ToolStripButton();
            toolStripSeparator2 = new ToolStripSeparator();
            btnSalesInvoice = new ToolStripButton();
            btnPurchaseInvoice = new ToolStripButton();
            toolStripSeparator3 = new ToolStripSeparator();
            btnCashBoxes = new ToolStripButton();
            btnExpenses = new ToolStripButton();
            toolStripSeparator4 = new ToolStripSeparator();
            btnSalesReports = new ToolStripButton();
            btnUsers = new ToolStripButton();
            btnBackup = new ToolStripButton();
            statusStrip1 = new StatusStrip();
            timer1 = new System.Windows.Forms.Timer(components);
            btnSales = new ToolStripButton();
            btnPurchases = new ToolStripButton();
            btnInventory = new ToolStripButton();
            btnReports = new ToolStripButton();
            btnSettings = new ToolStripButton();
            menuStrip1.SuspendLayout();
            toolStrip1.SuspendLayout();
            SuspendLayout();
            // 
            // menuStrip1
            // 
            menuStrip1.ImageScalingSize = new Size(20, 20);
            menuStrip1.Items.AddRange(new ToolStripItem[] { fileToolStripMenuItem, dataToolStripMenuItem, salesToolStripMenuItem, purchasesToolStripMenuItem, inventoryToolStripMenuItem, financialToolStripMenuItem, reportsToolStripMenuItem, settingsToolStripMenuItem });
            menuStrip1.Location = new Point(0, 0);
            menuStrip1.Name = "menuStrip1";
            menuStrip1.RightToLeft = RightToLeft.Yes;
            menuStrip1.Size = new Size(1200, 28);
            menuStrip1.TabIndex = 0;
            menuStrip1.Text = "menuStrip1";
            // 
            // fileToolStripMenuItem
            // 
            fileToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { logoutToolStripMenuItem, toolStripSeparator1, exitToolStripMenuItem });
            fileToolStripMenuItem.Name = "fileToolStripMenuItem";
            fileToolStripMenuItem.Size = new Size(46, 24);
            fileToolStripMenuItem.Text = "ملف";
            // 
            // logoutToolStripMenuItem
            // 
            logoutToolStripMenuItem.Name = "logoutToolStripMenuItem";
            logoutToolStripMenuItem.Size = new Size(180, 26);
            logoutToolStripMenuItem.Text = "تسجيل الخروج";
            logoutToolStripMenuItem.Click += logoutToolStripMenuItem_Click;
            // 
            // toolStripSeparator1
            // 
            toolStripSeparator1.Name = "toolStripSeparator1";
            toolStripSeparator1.Size = new Size(177, 6);
            // 
            // exitToolStripMenuItem
            // 
            exitToolStripMenuItem.Name = "exitToolStripMenuItem";
            exitToolStripMenuItem.Size = new Size(180, 26);
            exitToolStripMenuItem.Text = "خروج";
            exitToolStripMenuItem.Click += exitToolStripMenuItem_Click;
            // 
            // dataToolStripMenuItem
            // 
            dataToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { customersToolStripMenuItem, suppliersToolStripMenuItem, productsToolStripMenuItem, categoriesToolStripMenuItem });
            dataToolStripMenuItem.Name = "dataToolStripMenuItem";
            dataToolStripMenuItem.Size = new Size(73, 24);
            dataToolStripMenuItem.Text = "البيانات";
            // 
            // customersToolStripMenuItem
            // 
            customersToolStripMenuItem.Name = "customersToolStripMenuItem";
            customersToolStripMenuItem.Size = new Size(180, 26);
            customersToolStripMenuItem.Text = "العملاء";
            customersToolStripMenuItem.Click += customersToolStripMenuItem_Click;
            // 
            // suppliersToolStripMenuItem
            // 
            suppliersToolStripMenuItem.Name = "suppliersToolStripMenuItem";
            suppliersToolStripMenuItem.Size = new Size(180, 26);
            suppliersToolStripMenuItem.Text = "الموردين";
            suppliersToolStripMenuItem.Click += suppliersToolStripMenuItem_Click;
            // 
            // productsToolStripMenuItem
            // 
            productsToolStripMenuItem.Name = "productsToolStripMenuItem";
            productsToolStripMenuItem.Size = new Size(180, 26);
            productsToolStripMenuItem.Text = "المنتجات";
            productsToolStripMenuItem.Click += productsToolStripMenuItem_Click;
            // 
            // categoriesToolStripMenuItem
            // 
            categoriesToolStripMenuItem.Name = "categoriesToolStripMenuItem";
            categoriesToolStripMenuItem.Size = new Size(180, 26);
            categoriesToolStripMenuItem.Text = "التصنيفات";
            // 
            // salesToolStripMenuItem
            // 
            salesToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { salesInvoiceToolStripMenuItem, salesReturnsToolStripMenuItem });
            salesToolStripMenuItem.Name = "salesToolStripMenuItem";
            salesToolStripMenuItem.Size = new Size(73, 24);
            salesToolStripMenuItem.Text = "المبيعات";
            // 
            // salesInvoiceToolStripMenuItem
            // 
            salesInvoiceToolStripMenuItem.Name = "salesInvoiceToolStripMenuItem";
            salesInvoiceToolStripMenuItem.Size = new Size(180, 26);
            salesInvoiceToolStripMenuItem.Text = "فاتورة مبيعات";
            salesInvoiceToolStripMenuItem.Click += salesInvoiceToolStripMenuItem_Click;
            // 
            // salesReturnsToolStripMenuItem
            // 
            salesReturnsToolStripMenuItem.Name = "salesReturnsToolStripMenuItem";
            salesReturnsToolStripMenuItem.Size = new Size(180, 26);
            salesReturnsToolStripMenuItem.Text = "مرتجع مبيعات";
            // 
            // purchasesToolStripMenuItem
            // 
            purchasesToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { purchaseInvoiceToolStripMenuItem, purchaseReturnsToolStripMenuItem });
            purchasesToolStripMenuItem.Name = "purchasesToolStripMenuItem";
            purchasesToolStripMenuItem.Size = new Size(85, 24);
            purchasesToolStripMenuItem.Text = "المشتريات";
            // 
            // purchaseInvoiceToolStripMenuItem
            // 
            purchaseInvoiceToolStripMenuItem.Name = "purchaseInvoiceToolStripMenuItem";
            purchaseInvoiceToolStripMenuItem.Size = new Size(180, 26);
            purchaseInvoiceToolStripMenuItem.Text = "فاتورة مشتريات";
            purchaseInvoiceToolStripMenuItem.Click += purchaseInvoiceToolStripMenuItem_Click;
            // 
            // purchaseReturnsToolStripMenuItem
            // 
            purchaseReturnsToolStripMenuItem.Name = "purchaseReturnsToolStripMenuItem";
            purchaseReturnsToolStripMenuItem.Size = new Size(180, 26);
            purchaseReturnsToolStripMenuItem.Text = "مرتجع مشتريات";
            // 
            // inventoryToolStripMenuItem
            // 
            inventoryToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { inventoryMovementsToolStripMenuItem, stockAdjustmentToolStripMenuItem });
            inventoryToolStripMenuItem.Name = "inventoryToolStripMenuItem";
            inventoryToolStripMenuItem.Size = new Size(68, 24);
            inventoryToolStripMenuItem.Text = "المخزون";
            // 
            // inventoryMovementsToolStripMenuItem
            // 
            inventoryMovementsToolStripMenuItem.Name = "inventoryMovementsToolStripMenuItem";
            inventoryMovementsToolStripMenuItem.Size = new Size(180, 26);
            inventoryMovementsToolStripMenuItem.Text = "حركة المخزون";
            // 
            // stockAdjustmentToolStripMenuItem
            // 
            stockAdjustmentToolStripMenuItem.Name = "stockAdjustmentToolStripMenuItem";
            stockAdjustmentToolStripMenuItem.Size = new Size(180, 26);
            stockAdjustmentToolStripMenuItem.Text = "تعديل المخزون";
            // 
            // financialToolStripMenuItem
            // 
            financialToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { cashBoxesToolStripMenuItem, expensesToolStripMenuItem, customerPaymentsToolStripMenuItem, supplierPaymentsToolStripMenuItem });
            financialToolStripMenuItem.Name = "financialToolStripMenuItem";
            financialToolStripMenuItem.Size = new Size(62, 24);
            financialToolStripMenuItem.Text = "المالية";
            // 
            // cashBoxesToolStripMenuItem
            // 
            cashBoxesToolStripMenuItem.Name = "cashBoxesToolStripMenuItem";
            cashBoxesToolStripMenuItem.Size = new Size(180, 26);
            cashBoxesToolStripMenuItem.Text = "الخزائن";
            cashBoxesToolStripMenuItem.Click += cashBoxesToolStripMenuItem_Click;
            // 
            // expensesToolStripMenuItem
            // 
            expensesToolStripMenuItem.Name = "expensesToolStripMenuItem";
            expensesToolStripMenuItem.Size = new Size(180, 26);
            expensesToolStripMenuItem.Text = "المصروفات";
            expensesToolStripMenuItem.Click += expensesToolStripMenuItem_Click;
            // 
            // customerPaymentsToolStripMenuItem
            // 
            customerPaymentsToolStripMenuItem.Name = "customerPaymentsToolStripMenuItem";
            customerPaymentsToolStripMenuItem.Size = new Size(180, 26);
            customerPaymentsToolStripMenuItem.Text = "دفعات العملاء";
            // 
            // supplierPaymentsToolStripMenuItem
            // 
            supplierPaymentsToolStripMenuItem.Name = "supplierPaymentsToolStripMenuItem";
            supplierPaymentsToolStripMenuItem.Size = new Size(180, 26);
            supplierPaymentsToolStripMenuItem.Text = "دفعات الموردين";
            // 
            // reportsToolStripMenuItem
            // 
            reportsToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { salesReportsToolStripMenuItem, purchaseReportsToolStripMenuItem, inventoryReportsToolStripMenuItem, financialReportsToolStripMenuItem });
            reportsToolStripMenuItem.Name = "reportsToolStripMenuItem";
            reportsToolStripMenuItem.Size = new Size(67, 24);
            reportsToolStripMenuItem.Text = "التقارير";
            // 
            // salesReportsToolStripMenuItem
            // 
            salesReportsToolStripMenuItem.Name = "salesReportsToolStripMenuItem";
            salesReportsToolStripMenuItem.Size = new Size(180, 26);
            salesReportsToolStripMenuItem.Text = "تقارير المبيعات";
            salesReportsToolStripMenuItem.Click += salesReportsToolStripMenuItem_Click;
            // 
            // purchaseReportsToolStripMenuItem
            // 
            purchaseReportsToolStripMenuItem.Name = "purchaseReportsToolStripMenuItem";
            purchaseReportsToolStripMenuItem.Size = new Size(180, 26);
            purchaseReportsToolStripMenuItem.Text = "تقارير المشتريات";
            // 
            // inventoryReportsToolStripMenuItem
            // 
            inventoryReportsToolStripMenuItem.Name = "inventoryReportsToolStripMenuItem";
            inventoryReportsToolStripMenuItem.Size = new Size(180, 26);
            inventoryReportsToolStripMenuItem.Text = "تقارير المخزون";
            // 
            // financialReportsToolStripMenuItem
            // 
            financialReportsToolStripMenuItem.Name = "financialReportsToolStripMenuItem";
            financialReportsToolStripMenuItem.Size = new Size(180, 26);
            financialReportsToolStripMenuItem.Text = "التقارير المالية";
            // 
            // settingsToolStripMenuItem
            // 
            settingsToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { usersToolStripMenuItem, backupToolStripMenuItem, companySettingsToolStripMenuItem });
            settingsToolStripMenuItem.Name = "settingsToolStripMenuItem";
            settingsToolStripMenuItem.Size = new Size(78, 24);
            settingsToolStripMenuItem.Text = "الإعدادات";
            // 
            // usersToolStripMenuItem
            // 
            usersToolStripMenuItem.Name = "usersToolStripMenuItem";
            usersToolStripMenuItem.Size = new Size(180, 26);
            usersToolStripMenuItem.Text = "المستخدمين";
            usersToolStripMenuItem.Click += usersToolStripMenuItem_Click;
            // 
            // backupToolStripMenuItem
            // 
            backupToolStripMenuItem.Name = "backupToolStripMenuItem";
            backupToolStripMenuItem.Size = new Size(180, 26);
            backupToolStripMenuItem.Text = "النسخ الاحتياطي";
            backupToolStripMenuItem.Click += backupToolStripMenuItem_Click;
            // 
            // companySettingsToolStripMenuItem
            // 
            companySettingsToolStripMenuItem.Name = "companySettingsToolStripMenuItem";
            companySettingsToolStripMenuItem.Size = new Size(180, 26);
            companySettingsToolStripMenuItem.Text = "إعدادات الشركة";
            // 
            // toolStrip1
            // 
            toolStrip1.ImageScalingSize = new Size(32, 32);
            toolStrip1.Items.AddRange(new ToolStripItem[] { btnCustomers, btnSuppliers, btnProducts, toolStripSeparator2, btnSalesInvoice, btnPurchaseInvoice, toolStripSeparator3, btnCashBoxes, btnExpenses, toolStripSeparator4, btnSalesReports, btnUsers, btnBackup });
            toolStrip1.Location = new Point(0, 28);
            toolStrip1.Name = "toolStrip1";
            toolStrip1.RightToLeft = RightToLeft.Yes;
            toolStrip1.Size = new Size(1200, 39);
            toolStrip1.TabIndex = 1;
            toolStrip1.Text = "toolStrip1";
            // 
            // btnCustomers
            // 
            btnCustomers.Image = Properties.Resources.customers;
            btnCustomers.ImageTransparentColor = Color.Magenta;
            btnCustomers.Name = "btnCustomers";
            btnCustomers.Size = new Size(76, 36);
            btnCustomers.Text = "العملاء";
            btnCustomers.Click += btnCustomers_Click;
            // 
            // btnSuppliers
            // 
            btnSuppliers.Image = Properties.Resources.suppliers;
            btnSuppliers.ImageTransparentColor = Color.Magenta;
            btnSuppliers.Name = "btnSuppliers";
            btnSuppliers.Size = new Size(88, 36);
            btnSuppliers.Text = "الموردين";
            btnSuppliers.Click += btnSuppliers_Click;
            // 
            // btnProducts
            // 
            btnProducts.Image = Properties.Resources.products;
            btnProducts.ImageTransparentColor = Color.Magenta;
            btnProducts.Name = "btnProducts";
            btnProducts.Size = new Size(88, 36);
            btnProducts.Text = "المنتجات";
            btnProducts.Click += btnProducts_Click;
            // 
            // toolStripSeparator2
            // 
            toolStripSeparator2.Name = "toolStripSeparator2";
            toolStripSeparator2.Size = new Size(6, 39);
            // 
            // btnSalesInvoice
            // 
            btnSalesInvoice.Image = Properties.Resources.sales;
            btnSalesInvoice.ImageTransparentColor = Color.Magenta;
            btnSalesInvoice.Name = "btnSalesInvoice";
            btnSalesInvoice.Size = new Size(112, 36);
            btnSalesInvoice.Text = "فاتورة مبيعات";
            btnSalesInvoice.Click += btnSalesInvoice_Click;
            // 
            // btnPurchaseInvoice
            // 
            btnPurchaseInvoice.Image = Properties.Resources.purchases;
            btnPurchaseInvoice.ImageTransparentColor = Color.Magenta;
            btnPurchaseInvoice.Name = "btnPurchaseInvoice";
            btnPurchaseInvoice.Size = new Size(124, 36);
            btnPurchaseInvoice.Text = "فاتورة مشتريات";
            btnPurchaseInvoice.Click += btnPurchaseInvoice_Click;
            // 
            // toolStripSeparator3
            // 
            toolStripSeparator3.Name = "toolStripSeparator3";
            toolStripSeparator3.Size = new Size(6, 39);
            // 
            // btnCashBoxes
            // 
            btnCashBoxes.Image = Properties.Resources.cashbox;
            btnCashBoxes.ImageTransparentColor = Color.Magenta;
            btnCashBoxes.Name = "btnCashBoxes";
            btnCashBoxes.Size = new Size(76, 36);
            btnCashBoxes.Text = "الخزائن";
            btnCashBoxes.Click += btnCashBoxes_Click;
            // 
            // btnExpenses
            // 
            btnExpenses.Image = Properties.Resources.expenses;
            btnExpenses.ImageTransparentColor = Color.Magenta;
            btnExpenses.Name = "btnExpenses";
            btnExpenses.Size = new Size(100, 36);
            btnExpenses.Text = "المصروفات";
            btnExpenses.Click += btnExpenses_Click;
            // 
            // toolStripSeparator4
            // 
            toolStripSeparator4.Name = "toolStripSeparator4";
            toolStripSeparator4.Size = new Size(6, 39);
            // 
            // btnSalesReports
            // 
            btnSalesReports.Image = Properties.Resources.reports;
            btnSalesReports.ImageTransparentColor = Color.Magenta;
            btnSalesReports.Name = "btnSalesReports";
            btnSalesReports.Size = new Size(79, 36);
            btnSalesReports.Text = "التقارير";
            btnSalesReports.Click += btnSalesReports_Click;
            // 
            // btnUsers
            // 
            btnUsers.Image = Properties.Resources.users;
            btnUsers.ImageTransparentColor = Color.Magenta;
            btnUsers.Name = "btnUsers";
            btnUsers.Size = new Size(100, 36);
            btnUsers.Text = "المستخدمين";
            btnUsers.Click += btnUsers_Click;
            // 
            // btnBackup
            // 
            btnBackup.Image = Properties.Resources.backup;
            btnBackup.ImageTransparentColor = Color.Magenta;
            btnBackup.Name = "btnBackup";
            btnBackup.Size = new Size(127, 36);
            btnBackup.Text = "النسخ الاحتياطي";
            btnBackup.Click += btnBackup_Click;
            // 
            // statusStrip1
            // 
            statusStrip1.ImageScalingSize = new Size(20, 20);
            statusStrip1.Location = new Point(0, 728);
            statusStrip1.Name = "statusStrip1";
            statusStrip1.RightToLeft = RightToLeft.Yes;
            statusStrip1.Size = new Size(1200, 22);
            statusStrip1.TabIndex = 2;
            statusStrip1.Text = "statusStrip1";
            // 
            // timer1
            // 
            timer1.Tick += timer1_Tick;
            // 
            // btnSales
            // 
            btnSales.Name = "btnSales";
            btnSales.Size = new Size(23, 23);
            // 
            // btnPurchases
            // 
            btnPurchases.Name = "btnPurchases";
            btnPurchases.Size = new Size(23, 23);
            // 
            // btnInventory
            // 
            btnInventory.Name = "btnInventory";
            btnInventory.Size = new Size(23, 23);
            // 
            // btnReports
            // 
            btnReports.Name = "btnReports";
            btnReports.Size = new Size(23, 23);
            // 
            // btnSettings
            // 
            btnSettings.Name = "btnSettings";
            btnSettings.Size = new Size(23, 23);
            // 
            // MainForm
            // 
            AutoScaleDimensions = new SizeF(8F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1200, 750);
            Controls.Add(statusStrip1);
            Controls.Add(toolStrip1);
            Controls.Add(menuStrip1);
            IsMdiContainer = true;
            MainMenuStrip = menuStrip1;
            Name = "MainForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "SmartBooks - نظام إدارة المبيعات والمشتريات";
            Load += MainForm_Load;
            menuStrip1.ResumeLayout(false);
            menuStrip1.PerformLayout();
            toolStrip1.ResumeLayout(false);
            toolStrip1.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private MenuStrip menuStrip1;
        private ToolStripMenuItem fileToolStripMenuItem;
        private ToolStripMenuItem logoutToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripMenuItem exitToolStripMenuItem;
        private ToolStripMenuItem dataToolStripMenuItem;
        private ToolStripMenuItem customersToolStripMenuItem;
        private ToolStripMenuItem suppliersToolStripMenuItem;
        private ToolStripMenuItem productsToolStripMenuItem;
        private ToolStripMenuItem categoriesToolStripMenuItem;
        private ToolStripMenuItem salesToolStripMenuItem;
        private ToolStripMenuItem salesInvoiceToolStripMenuItem;
        private ToolStripMenuItem salesReturnsToolStripMenuItem;
        private ToolStripMenuItem purchasesToolStripMenuItem;
        private ToolStripMenuItem purchaseInvoiceToolStripMenuItem;
        private ToolStripMenuItem purchaseReturnsToolStripMenuItem;
        private ToolStripMenuItem inventoryToolStripMenuItem;
        private ToolStripMenuItem inventoryMovementsToolStripMenuItem;
        private ToolStripMenuItem stockAdjustmentToolStripMenuItem;
        private ToolStripMenuItem financialToolStripMenuItem;
        private ToolStripMenuItem cashBoxesToolStripMenuItem;
        private ToolStripMenuItem expensesToolStripMenuItem;
        private ToolStripMenuItem customerPaymentsToolStripMenuItem;
        private ToolStripMenuItem supplierPaymentsToolStripMenuItem;
        private ToolStripMenuItem reportsToolStripMenuItem;
        private ToolStripMenuItem salesReportsToolStripMenuItem;
        private ToolStripMenuItem purchaseReportsToolStripMenuItem;
        private ToolStripMenuItem inventoryReportsToolStripMenuItem;
        private ToolStripMenuItem financialReportsToolStripMenuItem;
        private ToolStripMenuItem settingsToolStripMenuItem;
        private ToolStripMenuItem usersToolStripMenuItem;
        private ToolStripMenuItem backupToolStripMenuItem;
        private ToolStripMenuItem companySettingsToolStripMenuItem;
        private ToolStrip toolStrip1;
        private ToolStripButton btnCustomers;
        private ToolStripButton btnSuppliers;
        private ToolStripButton btnProducts;
        private ToolStripSeparator toolStripSeparator2;
        private ToolStripButton btnSalesInvoice;
        private ToolStripButton btnPurchaseInvoice;
        private ToolStripSeparator toolStripSeparator3;
        private ToolStripButton btnCashBoxes;
        private ToolStripButton btnExpenses;
        private ToolStripSeparator toolStripSeparator4;
        private ToolStripButton btnSalesReports;
        private ToolStripButton btnUsers;
        private ToolStripButton btnBackup;
        private StatusStrip statusStrip1;
        private System.Windows.Forms.Timer timer1;
        private ToolStripButton btnSales;
        private ToolStripButton btnPurchases;
        private ToolStripButton btnInventory;
        private ToolStripButton btnReports;
        private ToolStripButton btnSettings;
    }
}
