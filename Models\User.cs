namespace SmartBooks.Models
{
    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Role { get; set; } = "User";
        public bool IsActive { get; set; } = true;
        public bool CanAccessSales { get; set; } = true;
        public bool CanAccessPurchases { get; set; } = true;
        public bool CanAccessInventory { get; set; } = true;
        public bool CanAccessReports { get; set; } = true;
        public bool CanAccessSettings { get; set; } = false;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? LastLogin { get; set; }
    }
}
