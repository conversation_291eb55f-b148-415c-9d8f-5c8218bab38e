using SmartBooks.Models;
using SmartBooks.DAL;

namespace SmartBooks.Forms
{
    public partial class MainForm : Form
    {
        private User? _currentUser;
        private ToolStripStatusLabel? _statusUser;
        private ToolStripStatusLabel? _statusDate;

        public MainForm()
        {
            InitializeComponent();
            ShowLoginForm();
        }

        private void ShowLoginForm()
        {
            using var loginForm = new LoginForm();
            if (loginForm.ShowDialog() == DialogResult.OK)
            {
                _currentUser = loginForm.CurrentUser;
                SetupMainForm();
                UpdateStatusBar();
            }
            else
            {
                Application.Exit();
            }
        }

        private void SetupMainForm()
        {
            if (_currentUser == null) return;

            // Set form properties
            this.Text = $"SmartBooks - نظام إدارة المبيعات والمشتريات - {_currentUser.FullName}";
            this.WindowState = FormWindowState.Maximized;

            // Setup permissions
            SetupPermissions();
        }

        private void SetupPermissions()
        {
            if (_currentUser == null) return;

            // Sales permissions
            salesToolStripMenuItem.Enabled = _currentUser.CanAccessSales;
            btnSales.Enabled = _currentUser.CanAccessSales;

            // Purchases permissions
            purchasesToolStripMenuItem.Enabled = _currentUser.CanAccessPurchases;
            btnPurchases.Enabled = _currentUser.CanAccessPurchases;

            // Inventory permissions
            inventoryToolStripMenuItem.Enabled = _currentUser.CanAccessInventory;
            btnInventory.Enabled = _currentUser.CanAccessInventory;

            // Reports permissions
            reportsToolStripMenuItem.Enabled = _currentUser.CanAccessReports;
            btnReports.Enabled = _currentUser.CanAccessReports;

            // Settings permissions
            settingsToolStripMenuItem.Enabled = _currentUser.CanAccessSettings;
            btnSettings.Enabled = _currentUser.CanAccessSettings;
        }

        private void UpdateStatusBar()
        {
            if (_statusUser != null && _currentUser != null)
            {
                _statusUser.Text = $"المستخدم: {_currentUser.FullName}";
            }

            if (_statusDate != null)
            {
                _statusDate.Text = $"التاريخ: {DateTime.Now:yyyy/MM/dd - HH:mm}";
            }
        }

        // Event handlers for menu items and buttons
        private void customersToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ShowCustomersForm();
        }

        private void btnCustomers_Click(object sender, EventArgs e)
        {
            ShowCustomersForm();
        }

        private void ShowCustomersForm()
        {
            var customersForm = new CustomersForm();
            customersForm.MdiParent = this;
            customersForm.Show();
        }

        private void suppliersToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ShowSuppliersForm();
        }

        private void btnSuppliers_Click(object sender, EventArgs e)
        {
            ShowSuppliersForm();
        }

        private void ShowSuppliersForm()
        {
            var suppliersForm = new SuppliersForm();
            suppliersForm.MdiParent = this;
            suppliersForm.Show();
        }

        private void productsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ShowProductsForm();
        }

        private void btnProducts_Click(object sender, EventArgs e)
        {
            ShowProductsForm();
        }

        private void ShowProductsForm()
        {
            var productsForm = new ProductsForm();
            productsForm.MdiParent = this;
            productsForm.Show();
        }

        private void salesInvoiceToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ShowSalesInvoiceForm();
        }

        private void btnSalesInvoice_Click(object sender, EventArgs e)
        {
            ShowSalesInvoiceForm();
        }

        private void ShowSalesInvoiceForm()
        {
            var salesForm = new SalesInvoiceForm(_currentUser);
            salesForm.MdiParent = this;
            salesForm.Show();
        }

        private void purchaseInvoiceToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ShowPurchaseInvoiceForm();
        }

        private void btnPurchaseInvoice_Click(object sender, EventArgs e)
        {
            ShowPurchaseInvoiceForm();
        }

        private void ShowPurchaseInvoiceForm()
        {
            var purchaseForm = new PurchaseInvoiceForm(_currentUser);
            purchaseForm.MdiParent = this;
            purchaseForm.Show();
        }

        private void cashBoxesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ShowCashBoxesForm();
        }

        private void btnCashBoxes_Click(object sender, EventArgs e)
        {
            ShowCashBoxesForm();
        }

        private void ShowCashBoxesForm()
        {
            var cashBoxForm = new CashBoxesForm(_currentUser);
            cashBoxForm.MdiParent = this;
            cashBoxForm.Show();
        }

        private void expensesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ShowExpensesForm();
        }

        private void btnExpenses_Click(object sender, EventArgs e)
        {
            ShowExpensesForm();
        }

        private void ShowExpensesForm()
        {
            var expensesForm = new ExpensesForm(_currentUser);
            expensesForm.MdiParent = this;
            expensesForm.Show();
        }

        private void salesReportsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ShowSalesReportsForm();
        }

        private void btnSalesReports_Click(object sender, EventArgs e)
        {
            ShowSalesReportsForm();
        }

        private void ShowSalesReportsForm()
        {
            var reportsForm = new SalesReportsForm();
            reportsForm.MdiParent = this;
            reportsForm.Show();
        }

        private void usersToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ShowUsersForm();
        }

        private void btnUsers_Click(object sender, EventArgs e)
        {
            ShowUsersForm();
        }

        private void ShowUsersForm()
        {
            var usersForm = new UsersForm();
            usersForm.MdiParent = this;
            usersForm.Show();
        }

        private void backupToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ShowBackupForm();
        }

        private void btnBackup_Click(object sender, EventArgs e)
        {
            ShowBackupForm();
        }

        private void ShowBackupForm()
        {
            var backupForm = new BackupForm();
            backupForm.ShowDialog();
        }

        private void exitToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void logoutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                // Close all MDI children
                foreach (Form childForm in this.MdiChildren)
                {
                    childForm.Close();
                }

                // Show login form again
                ShowLoginForm();
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            UpdateStatusBar();
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            // Start timer for status bar updates
            timer1.Interval = 60000; // Update every minute
            timer1.Start();
        }
    }
}
