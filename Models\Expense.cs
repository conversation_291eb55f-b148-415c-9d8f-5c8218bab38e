namespace SmartBooks.Models
{
    public class ExpenseType
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    public class Expense
    {
        public int Id { get; set; }
        public int ExpenseTypeId { get; set; }
        public int CashBoxId { get; set; }
        public decimal Amount { get; set; }
        public string Description { get; set; } = string.Empty;
        public DateTime ExpenseDate { get; set; } = DateTime.Now;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public int? UserId { get; set; }

        // Navigation properties
        public ExpenseType? ExpenseType { get; set; }
        public CashBox? CashBox { get; set; }
        public User? User { get; set; }
    }
}
