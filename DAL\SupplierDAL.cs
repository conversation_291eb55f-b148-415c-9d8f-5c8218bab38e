using SmartBooks.Models;
using System.Data.SQLite;
using System.Data;

namespace SmartBooks.DAL
{
    public class SupplierDAL : BaseDAL
    {
        public List<Supplier> GetAllSuppliers()
        {
            string sql = @"
                SELECT Id, Name, Phone, Email, Address, TaxNumber, 
                       CurrentBalance, IsActive, CreatedDate, Notes
                FROM Suppliers 
                ORDER BY Name";

            var dataTable = ExecuteQuery(sql);
            var suppliers = new List<Supplier>();

            foreach (DataRow row in dataTable.Rows)
            {
                suppliers.Add(MapRowToSupplier(row));
            }

            return suppliers;
        }

        public List<Supplier> GetActiveSuppliers()
        {
            string sql = @"
                SELECT Id, Name, Phone, Email, Address, TaxNumber, 
                       CurrentBalance, IsActive, CreatedDate, Notes
                FROM Suppliers 
                WHERE IsActive = 1
                ORDER BY Name";

            var dataTable = ExecuteQuery(sql);
            var suppliers = new List<Supplier>();

            foreach (DataRow row in dataTable.Rows)
            {
                suppliers.Add(MapRowToSupplier(row));
            }

            return suppliers;
        }

        public Supplier? GetSupplierById(int id)
        {
            string sql = @"
                SELECT Id, Name, Phone, Email, Address, TaxNumber, 
                       CurrentBalance, IsActive, CreatedDate, Notes
                FROM Suppliers 
                WHERE Id = @id";

            var parameters = new[] { CreateParameter("@id", id) };
            var dataTable = ExecuteQuery(sql, parameters);

            if (dataTable.Rows.Count == 0)
                return null;

            return MapRowToSupplier(dataTable.Rows[0]);
        }

        public int AddSupplier(Supplier supplier)
        {
            string sql = @"
                INSERT INTO Suppliers (Name, Phone, Email, Address, TaxNumber, 
                                     CurrentBalance, IsActive, Notes)
                VALUES (@name, @phone, @email, @address, @taxNumber, 
                        @currentBalance, @isActive, @notes);
                SELECT last_insert_rowid();";

            var parameters = new[]
            {
                CreateParameter("@name", supplier.Name),
                CreateParameter("@phone", supplier.Phone),
                CreateParameter("@email", supplier.Email),
                CreateParameter("@address", supplier.Address),
                CreateParameter("@taxNumber", supplier.TaxNumber),
                CreateParameter("@currentBalance", supplier.CurrentBalance),
                CreateParameter("@isActive", supplier.IsActive),
                CreateParameter("@notes", supplier.Notes)
            };

            return ExecuteScalar<int>(sql, parameters);
        }

        public bool UpdateSupplier(Supplier supplier)
        {
            string sql = @"
                UPDATE Suppliers 
                SET Name = @name, Phone = @phone, Email = @email, Address = @address, 
                    TaxNumber = @taxNumber, CurrentBalance = @currentBalance, 
                    IsActive = @isActive, Notes = @notes
                WHERE Id = @id";

            var parameters = new[]
            {
                CreateParameter("@id", supplier.Id),
                CreateParameter("@name", supplier.Name),
                CreateParameter("@phone", supplier.Phone),
                CreateParameter("@email", supplier.Email),
                CreateParameter("@address", supplier.Address),
                CreateParameter("@taxNumber", supplier.TaxNumber),
                CreateParameter("@currentBalance", supplier.CurrentBalance),
                CreateParameter("@isActive", supplier.IsActive),
                CreateParameter("@notes", supplier.Notes)
            };

            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public bool DeleteSupplier(int id)
        {
            string sql = "DELETE FROM Suppliers WHERE Id = @id";
            var parameters = new[] { CreateParameter("@id", id) };
            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public bool UpdateSupplierBalance(int supplierId, decimal amount)
        {
            string sql = "UPDATE Suppliers SET CurrentBalance = CurrentBalance + @amount WHERE Id = @id";
            var parameters = new[]
            {
                CreateParameter("@id", supplierId),
                CreateParameter("@amount", amount)
            };
            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public List<Supplier> SearchSuppliers(string searchTerm)
        {
            string sql = @"
                SELECT Id, Name, Phone, Email, Address, TaxNumber, 
                       CurrentBalance, IsActive, CreatedDate, Notes
                FROM Suppliers 
                WHERE Name LIKE @searchTerm OR Phone LIKE @searchTerm OR Email LIKE @searchTerm
                ORDER BY Name";

            var parameters = new[] { CreateParameter("@searchTerm", $"%{searchTerm}%") };
            var dataTable = ExecuteQuery(sql, parameters);
            var suppliers = new List<Supplier>();

            foreach (DataRow row in dataTable.Rows)
            {
                suppliers.Add(MapRowToSupplier(row));
            }

            return suppliers;
        }

        public decimal GetSupplierTotalPurchases(int supplierId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            string sql = "SELECT COALESCE(SUM(NetAmount), 0) FROM PurchaseInvoices WHERE SupplierId = @supplierId";
            var parameters = new List<SQLiteParameter> { CreateParameter("@supplierId", supplierId) };

            if (fromDate.HasValue)
            {
                sql += " AND InvoiceDate >= @fromDate";
                parameters.Add(CreateParameter("@fromDate", fromDate.Value.ToString("yyyy-MM-dd")));
            }

            if (toDate.HasValue)
            {
                sql += " AND InvoiceDate <= @toDate";
                parameters.Add(CreateParameter("@toDate", toDate.Value.ToString("yyyy-MM-dd")));
            }

            return ExecuteScalar<decimal>(sql, parameters.ToArray());
        }

        private Supplier MapRowToSupplier(DataRow row)
        {
            return new Supplier
            {
                Id = Convert.ToInt32(row["Id"]),
                Name = GetString(row["Name"]),
                Phone = GetString(row["Phone"]),
                Email = GetString(row["Email"]),
                Address = GetString(row["Address"]),
                TaxNumber = GetString(row["TaxNumber"]),
                CurrentBalance = GetDecimal(row["CurrentBalance"]),
                IsActive = GetBoolean(row["IsActive"]),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                Notes = GetString(row["Notes"])
            };
        }
    }
}
