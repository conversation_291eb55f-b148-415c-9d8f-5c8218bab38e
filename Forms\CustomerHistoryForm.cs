using SmartBooks.Models;
using SmartBooks.DAL;

namespace SmartBooks.Forms
{
    public partial class CustomerHistoryForm : Form
    {
        private Customer _customer;
        private CustomerDAL _customerDAL;

        public CustomerHistoryForm(Customer customer)
        {
            InitializeComponent();
            _customer = customer;
            _customerDAL = new CustomerDAL();
        }

        private void CustomerHistoryForm_Load(object sender, EventArgs e)
        {
            this.Text = $"تاريخ العميل - {_customer.Name}";
            lblCustomerName.Text = $"العميل: {_customer.Name}";
            lblCustomerPhone.Text = $"الهاتف: {_customer.Phone}";
            lblCustomerBalance.Text = $"الرصيد الحالي: {_customer.Balance:N2}";

            LoadSalesHistory();
        }

        private void LoadSalesHistory()
        {
            try
            {
                var salesHistory = _customerDAL.GetCustomerSalesHistory(_customer.Id);
                dgvSalesHistory.DataSource = salesHistory;

                if (salesHistory.Any())
                {
                    var totalSales = salesHistory.Sum(s => s.TotalAmount);
                    var totalPaid = salesHistory.Sum(s => s.PaidAmount);
                    var totalRemaining = salesHistory.Sum(s => s.RemainingAmount);

                    lblTotalSales.Text = $"إجمالي المبيعات: {totalSales:N2}";
                    lblTotalPaid.Text = $"إجمالي المدفوع: {totalPaid:N2}";
                    lblTotalRemaining.Text = $"إجمالي المتبقي: {totalRemaining:N2}";
                }
                else
                {
                    lblTotalSales.Text = "إجمالي المبيعات: 0.00";
                    lblTotalPaid.Text = "إجمالي المدفوع: 0.00";
                    lblTotalRemaining.Text = "إجمالي المتبقي: 0.00";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تاريخ العميل: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            // TODO: Implement printing functionality
            MessageBox.Show("سيتم تطوير وظيفة الطباعة لاحقاً", "معلومات", 
                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
