namespace SmartBooks.Models
{
    public class Product
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int? CategoryId { get; set; }
        public string Unit { get; set; } = "قطعة";
        public decimal PurchasePrice { get; set; } = 0;
        public decimal SalePrice { get; set; } = 0;
        public decimal MinStock { get; set; } = 0;
        public decimal CurrentStock { get; set; } = 0;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string Description { get; set; } = string.Empty;

        // Navigation properties
        public Category? Category { get; set; }
        public List<ProductBarcode> Barcodes { get; set; } = new List<ProductBarcode>();
    }

    public class ProductBarcode
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public string Barcode { get; set; } = string.Empty;
        public bool IsDefault { get; set; } = false;
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation property
        public Product? Product { get; set; }
    }
}
