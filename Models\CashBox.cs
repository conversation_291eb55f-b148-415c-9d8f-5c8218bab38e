namespace SmartBooks.Models
{
    public class CashBox
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public decimal CurrentBalance { get; set; } = 0;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string Description { get; set; } = string.Empty;
    }

    public class CashBoxTransaction
    {
        public int Id { get; set; }
        public int CashBoxId { get; set; }
        public string TransactionType { get; set; } = string.Empty; // إيداع، سحب، تحويل
        public decimal Amount { get; set; }
        public string Description { get; set; } = string.Empty;
        public string ReferenceType { get; set; } = string.Empty; // مبيعات، مشتريات، مصروفات، دفع عميل، دفع مورد، يدوي
        public int? ReferenceId { get; set; }
        public int? ToCashBoxId { get; set; } // للتحويلات
        public DateTime TransactionDate { get; set; } = DateTime.Now;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public int? UserId { get; set; }

        // Navigation properties
        public CashBox? CashBox { get; set; }
        public CashBox? ToCashBox { get; set; }
        public User? User { get; set; }
    }
}
