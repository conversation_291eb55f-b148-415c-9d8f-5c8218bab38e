using System.Data.SQLite;
using System.Data;

namespace SmartBooks.DAL
{
    public abstract class BaseDAL
    {
        protected string ConnectionString => DatabaseHelper.ConnectionString;

        protected SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(ConnectionString);
        }

        protected T ExecuteScalar<T>(string sql, params SQLiteParameter[] parameters)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(sql, connection);
            if (parameters != null)
                command.Parameters.AddRange(parameters);

            var result = command.ExecuteScalar();
            return result == null || result == DBNull.Value ? default(T)! : (T)Convert.ChangeType(result, typeof(T));
        }

        protected int ExecuteNonQuery(string sql, params SQLiteParameter[] parameters)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(sql, connection);
            if (parameters != null)
                command.Parameters.AddRange(parameters);

            return command.ExecuteNonQuery();
        }

        protected DataTable ExecuteQuery(string sql, params SQLiteParameter[] parameters)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(sql, connection);
            if (parameters != null)
                command.Parameters.AddRange(parameters);

            using var adapter = new SQLiteDataAdapter(command);
            var dataTable = new DataTable();
            adapter.Fill(dataTable);
            return dataTable;
        }

        protected SQLiteParameter CreateParameter(string name, object? value)
        {
            return new SQLiteParameter(name, value ?? DBNull.Value);
        }

        protected DateTime? GetNullableDateTime(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;
            return Convert.ToDateTime(value);
        }

        protected int? GetNullableInt(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;
            return Convert.ToInt32(value);
        }

        protected decimal GetDecimal(object value)
        {
            if (value == null || value == DBNull.Value)
                return 0;
            return Convert.ToDecimal(value);
        }

        protected bool GetBoolean(object value)
        {
            if (value == null || value == DBNull.Value)
                return false;
            return Convert.ToBoolean(value);
        }

        protected string GetString(object value)
        {
            if (value == null || value == DBNull.Value)
                return string.Empty;
            return value.ToString() ?? string.Empty;
        }
    }
}
