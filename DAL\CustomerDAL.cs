using SmartBooks.Models;
using System.Data.SQLite;
using System.Data;

namespace SmartBooks.DAL
{
    public class CustomerDAL : BaseDAL
    {
        public List<Customer> GetAllCustomers()
        {
            string sql = @"
                SELECT Id, Name, Phone, Email, Address, TaxNumber, 
                       CreditLimit, CurrentBalance, IsActive, CreatedDate, Notes
                FROM Customers 
                ORDER BY Name";

            var dataTable = ExecuteQuery(sql);
            var customers = new List<Customer>();

            foreach (DataRow row in dataTable.Rows)
            {
                customers.Add(MapRowToCustomer(row));
            }

            return customers;
        }

        public List<Customer> GetActiveCustomers()
        {
            string sql = @"
                SELECT Id, Name, Phone, Email, Address, TaxNumber, 
                       CreditLimit, CurrentBalance, IsActive, CreatedDate, Notes
                FROM Customers 
                WHERE IsActive = 1
                ORDER BY Name";

            var dataTable = ExecuteQuery(sql);
            var customers = new List<Customer>();

            foreach (DataRow row in dataTable.Rows)
            {
                customers.Add(MapRowToCustomer(row));
            }

            return customers;
        }

        public Customer? GetCustomerById(int id)
        {
            string sql = @"
                SELECT Id, Name, Phone, Email, Address, TaxNumber, 
                       CreditLimit, CurrentBalance, IsActive, CreatedDate, Notes
                FROM Customers 
                WHERE Id = @id";

            var parameters = new[] { CreateParameter("@id", id) };
            var dataTable = ExecuteQuery(sql, parameters);

            if (dataTable.Rows.Count == 0)
                return null;

            return MapRowToCustomer(dataTable.Rows[0]);
        }

        public int AddCustomer(Customer customer)
        {
            string sql = @"
                INSERT INTO Customers (Name, Phone, Email, Address, TaxNumber, 
                                     CreditLimit, CurrentBalance, IsActive, Notes)
                VALUES (@name, @phone, @email, @address, @taxNumber, 
                        @creditLimit, @currentBalance, @isActive, @notes);
                SELECT last_insert_rowid();";

            var parameters = new[]
            {
                CreateParameter("@name", customer.Name),
                CreateParameter("@phone", customer.Phone),
                CreateParameter("@email", customer.Email),
                CreateParameter("@address", customer.Address),
                CreateParameter("@taxNumber", customer.TaxNumber),
                CreateParameter("@creditLimit", customer.CreditLimit),
                CreateParameter("@currentBalance", customer.CurrentBalance),
                CreateParameter("@isActive", customer.IsActive),
                CreateParameter("@notes", customer.Notes)
            };

            return ExecuteScalar<int>(sql, parameters);
        }

        public bool UpdateCustomer(Customer customer)
        {
            string sql = @"
                UPDATE Customers 
                SET Name = @name, Phone = @phone, Email = @email, Address = @address, 
                    TaxNumber = @taxNumber, CreditLimit = @creditLimit, 
                    CurrentBalance = @currentBalance, IsActive = @isActive, Notes = @notes
                WHERE Id = @id";

            var parameters = new[]
            {
                CreateParameter("@id", customer.Id),
                CreateParameter("@name", customer.Name),
                CreateParameter("@phone", customer.Phone),
                CreateParameter("@email", customer.Email),
                CreateParameter("@address", customer.Address),
                CreateParameter("@taxNumber", customer.TaxNumber),
                CreateParameter("@creditLimit", customer.CreditLimit),
                CreateParameter("@currentBalance", customer.CurrentBalance),
                CreateParameter("@isActive", customer.IsActive),
                CreateParameter("@notes", customer.Notes)
            };

            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public bool DeleteCustomer(int id)
        {
            string sql = "DELETE FROM Customers WHERE Id = @id";
            var parameters = new[] { CreateParameter("@id", id) };
            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public bool UpdateCustomerBalance(int customerId, decimal amount)
        {
            string sql = "UPDATE Customers SET CurrentBalance = CurrentBalance + @amount WHERE Id = @id";
            var parameters = new[]
            {
                CreateParameter("@id", customerId),
                CreateParameter("@amount", amount)
            };
            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public List<Customer> SearchCustomers(string searchTerm)
        {
            string sql = @"
                SELECT Id, Name, Phone, Email, Address, TaxNumber, 
                       CreditLimit, CurrentBalance, IsActive, CreatedDate, Notes
                FROM Customers 
                WHERE Name LIKE @searchTerm OR Phone LIKE @searchTerm OR Email LIKE @searchTerm
                ORDER BY Name";

            var parameters = new[] { CreateParameter("@searchTerm", $"%{searchTerm}%") };
            var dataTable = ExecuteQuery(sql, parameters);
            var customers = new List<Customer>();

            foreach (DataRow row in dataTable.Rows)
            {
                customers.Add(MapRowToCustomer(row));
            }

            return customers;
        }

        public decimal GetCustomerTotalSales(int customerId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            string sql = "SELECT COALESCE(SUM(NetAmount), 0) FROM SalesInvoices WHERE CustomerId = @customerId";
            var parameters = new List<SQLiteParameter> { CreateParameter("@customerId", customerId) };

            if (fromDate.HasValue)
            {
                sql += " AND InvoiceDate >= @fromDate";
                parameters.Add(CreateParameter("@fromDate", fromDate.Value.ToString("yyyy-MM-dd")));
            }

            if (toDate.HasValue)
            {
                sql += " AND InvoiceDate <= @toDate";
                parameters.Add(CreateParameter("@toDate", toDate.Value.ToString("yyyy-MM-dd")));
            }

            return ExecuteScalar<decimal>(sql, parameters.ToArray());
        }

        private Customer MapRowToCustomer(DataRow row)
        {
            return new Customer
            {
                Id = Convert.ToInt32(row["Id"]),
                Name = GetString(row["Name"]),
                Phone = GetString(row["Phone"]),
                Email = GetString(row["Email"]),
                Address = GetString(row["Address"]),
                TaxNumber = GetString(row["TaxNumber"]),
                CreditLimit = GetDecimal(row["CreditLimit"]),
                CurrentBalance = GetDecimal(row["CurrentBalance"]),
                IsActive = GetBoolean(row["IsActive"]),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                Notes = GetString(row["Notes"])
            };
        }
    }
}
