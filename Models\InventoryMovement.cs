namespace SmartBooks.Models
{
    public class InventoryMovement
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public string MovementType { get; set; } = string.Empty; // دخول، خروج، تعديل
        public decimal Quantity { get; set; }
        public string ReferenceType { get; set; } = string.Empty; // مبيعات، مشتريات، مرتجع مبيعات، مرتجع مشتريات، تعديل يدوي
        public int? ReferenceId { get; set; }
        public DateTime MovementDate { get; set; } = DateTime.Now;
        public string Notes { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public int? UserId { get; set; }

        // Navigation properties
        public Product? Product { get; set; }
        public User? User { get; set; }
    }
}
