namespace SmartBooks.Models
{
    public class SalesReturn
    {
        public int Id { get; set; }
        public string ReturnNumber { get; set; } = string.Empty;
        public int? OriginalInvoiceId { get; set; }
        public int? CustomerId { get; set; }
        public DateTime ReturnDate { get; set; } = DateTime.Now;
        public decimal TotalAmount { get; set; } = 0;
        public int? CashBoxId { get; set; }
        public string Notes { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public int? UserId { get; set; }

        // Navigation properties
        public SalesInvoice? OriginalInvoice { get; set; }
        public Customer? Customer { get; set; }
        public CashBox? CashBox { get; set; }
        public User? User { get; set; }
        public List<SalesReturnItem> Items { get; set; } = new List<SalesReturnItem>();
    }

    public class SalesReturnItem
    {
        public int Id { get; set; }
        public int ReturnId { get; set; }
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }

        // Navigation properties
        public SalesReturn? Return { get; set; }
        public Product? Product { get; set; }
    }

    public class PurchaseReturn
    {
        public int Id { get; set; }
        public string ReturnNumber { get; set; } = string.Empty;
        public int? OriginalInvoiceId { get; set; }
        public int SupplierId { get; set; }
        public DateTime ReturnDate { get; set; } = DateTime.Now;
        public decimal TotalAmount { get; set; } = 0;
        public int? CashBoxId { get; set; }
        public string Notes { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public int? UserId { get; set; }

        // Navigation properties
        public PurchaseInvoice? OriginalInvoice { get; set; }
        public Supplier? Supplier { get; set; }
        public CashBox? CashBox { get; set; }
        public User? User { get; set; }
        public List<PurchaseReturnItem> Items { get; set; } = new List<PurchaseReturnItem>();
    }

    public class PurchaseReturnItem
    {
        public int Id { get; set; }
        public int ReturnId { get; set; }
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }

        // Navigation properties
        public PurchaseReturn? Return { get; set; }
        public Product? Product { get; set; }
    }
}
