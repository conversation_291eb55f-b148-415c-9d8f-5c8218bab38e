using SmartBooks.Forms;
using SmartBooks.DAL;
using System.Globalization;

namespace SmartBooks
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.
            ApplicationConfiguration.Initialize();

            // Set Arabic culture
            Thread.CurrentThread.CurrentCulture = new CultureInfo("ar-SA");
            Thread.CurrentThread.CurrentUICulture = new CultureInfo("ar-SA");

            // Initialize database
            DatabaseHelper.InitializeDatabase();

            // Run the main form
            Application.Run(new MainForm());
        }
    }
}
