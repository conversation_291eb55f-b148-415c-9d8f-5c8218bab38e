namespace SmartBooks.Forms
{
    partial class CustomerHistoryForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            panel1 = new Panel();
            lblCustomerBalance = new Label();
            lblCustomerPhone = new Label();
            lblCustomerName = new Label();
            dgvSalesHistory = new DataGridView();
            panel2 = new Panel();
            btnClose = new Button();
            btnPrint = new Button();
            lblTotalRemaining = new Label();
            lblTotalPaid = new Label();
            lblTotalSales = new Label();
            panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvSalesHistory).BeginInit();
            panel2.SuspendLayout();
            SuspendLayout();
            // 
            // panel1
            // 
            panel1.BackColor = Color.FromArgb(248, 249, 250);
            panel1.Controls.Add(lblCustomerBalance);
            panel1.Controls.Add(lblCustomerPhone);
            panel1.Controls.Add(lblCustomerName);
            panel1.Dock = DockStyle.Top;
            panel1.Location = new Point(0, 0);
            panel1.Name = "panel1";
            panel1.Size = new Size(1000, 100);
            panel1.TabIndex = 0;
            // 
            // lblCustomerBalance
            // 
            lblCustomerBalance.AutoSize = true;
            lblCustomerBalance.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblCustomerBalance.ForeColor = Color.FromArgb(220, 53, 69);
            lblCustomerBalance.Location = new Point(20, 70);
            lblCustomerBalance.Name = "lblCustomerBalance";
            lblCustomerBalance.Size = new Size(130, 23);
            lblCustomerBalance.TabIndex = 2;
            lblCustomerBalance.Text = "الرصيد الحالي: 0.00";
            // 
            // lblCustomerPhone
            // 
            lblCustomerPhone.AutoSize = true;
            lblCustomerPhone.Font = new Font("Segoe UI", 10F);
            lblCustomerPhone.Location = new Point(20, 45);
            lblCustomerPhone.Name = "lblCustomerPhone";
            lblCustomerPhone.Size = new Size(64, 23);
            lblCustomerPhone.TabIndex = 1;
            lblCustomerPhone.Text = "الهاتف: ";
            // 
            // lblCustomerName
            // 
            lblCustomerName.AutoSize = true;
            lblCustomerName.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblCustomerName.ForeColor = Color.FromArgb(0, 123, 255);
            lblCustomerName.Location = new Point(20, 15);
            lblCustomerName.Name = "lblCustomerName";
            lblCustomerName.Size = new Size(73, 28);
            lblCustomerName.TabIndex = 0;
            lblCustomerName.Text = "العميل: ";
            // 
            // dgvSalesHistory
            // 
            dgvSalesHistory.AllowUserToAddRows = false;
            dgvSalesHistory.AllowUserToDeleteRows = false;
            dgvSalesHistory.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvSalesHistory.BackgroundColor = Color.White;
            dgvSalesHistory.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvSalesHistory.Dock = DockStyle.Fill;
            dgvSalesHistory.Location = new Point(0, 100);
            dgvSalesHistory.MultiSelect = false;
            dgvSalesHistory.Name = "dgvSalesHistory";
            dgvSalesHistory.ReadOnly = true;
            dgvSalesHistory.RowHeadersWidth = 51;
            dgvSalesHistory.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvSalesHistory.Size = new Size(1000, 450);
            dgvSalesHistory.TabIndex = 1;
            // 
            // panel2
            // 
            panel2.BackColor = Color.FromArgb(248, 249, 250);
            panel2.Controls.Add(btnClose);
            panel2.Controls.Add(btnPrint);
            panel2.Controls.Add(lblTotalRemaining);
            panel2.Controls.Add(lblTotalPaid);
            panel2.Controls.Add(lblTotalSales);
            panel2.Dock = DockStyle.Bottom;
            panel2.Location = new Point(0, 550);
            panel2.Name = "panel2";
            panel2.Size = new Size(1000, 100);
            panel2.TabIndex = 2;
            // 
            // btnClose
            // 
            btnClose.BackColor = Color.FromArgb(220, 53, 69);
            btnClose.FlatStyle = FlatStyle.Flat;
            btnClose.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnClose.ForeColor = Color.White;
            btnClose.Location = new Point(880, 30);
            btnClose.Name = "btnClose";
            btnClose.Size = new Size(100, 40);
            btnClose.TabIndex = 4;
            btnClose.Text = "إغلاق";
            btnClose.UseVisualStyleBackColor = false;
            btnClose.Click += btnClose_Click;
            // 
            // btnPrint
            // 
            btnPrint.BackColor = Color.FromArgb(40, 167, 69);
            btnPrint.FlatStyle = FlatStyle.Flat;
            btnPrint.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnPrint.ForeColor = Color.White;
            btnPrint.Location = new Point(770, 30);
            btnPrint.Name = "btnPrint";
            btnPrint.Size = new Size(100, 40);
            btnPrint.TabIndex = 3;
            btnPrint.Text = "طباعة";
            btnPrint.UseVisualStyleBackColor = false;
            btnPrint.Click += btnPrint_Click;
            // 
            // lblTotalRemaining
            // 
            lblTotalRemaining.AutoSize = true;
            lblTotalRemaining.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblTotalRemaining.ForeColor = Color.FromArgb(220, 53, 69);
            lblTotalRemaining.Location = new Point(20, 70);
            lblTotalRemaining.Name = "lblTotalRemaining";
            lblTotalRemaining.Size = new Size(140, 23);
            lblTotalRemaining.TabIndex = 2;
            lblTotalRemaining.Text = "إجمالي المتبقي: 0.00";
            // 
            // lblTotalPaid
            // 
            lblTotalPaid.AutoSize = true;
            lblTotalPaid.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblTotalPaid.ForeColor = Color.FromArgb(40, 167, 69);
            lblTotalPaid.Location = new Point(20, 45);
            lblTotalPaid.Name = "lblTotalPaid";
            lblTotalPaid.Size = new Size(140, 23);
            lblTotalPaid.TabIndex = 1;
            lblTotalPaid.Text = "إجمالي المدفوع: 0.00";
            // 
            // lblTotalSales
            // 
            lblTotalSales.AutoSize = true;
            lblTotalSales.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblTotalSales.ForeColor = Color.FromArgb(0, 123, 255);
            lblTotalSales.Location = new Point(20, 20);
            lblTotalSales.Name = "lblTotalSales";
            lblTotalSales.Size = new Size(140, 23);
            lblTotalSales.TabIndex = 0;
            lblTotalSales.Text = "إجمالي المبيعات: 0.00";
            // 
            // CustomerHistoryForm
            // 
            AutoScaleDimensions = new SizeF(8F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1000, 650);
            Controls.Add(dgvSalesHistory);
            Controls.Add(panel2);
            Controls.Add(panel1);
            Name = "CustomerHistoryForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "تاريخ العميل";
            Load += CustomerHistoryForm_Load;
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dgvSalesHistory).EndInit();
            panel2.ResumeLayout(false);
            panel2.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private Panel panel1;
        private Label lblCustomerName;
        private Label lblCustomerPhone;
        private Label lblCustomerBalance;
        private DataGridView dgvSalesHistory;
        private Panel panel2;
        private Label lblTotalSales;
        private Label lblTotalPaid;
        private Label lblTotalRemaining;
        private Button btnPrint;
        private Button btnClose;
    }
}
