using SmartBooks.Models;
using System.Data.SQLite;
using System.Data;

namespace SmartBooks.DAL
{
    public class CategoryDAL : BaseDAL
    {
        public List<Category> GetAllCategories()
        {
            string sql = @"
                SELECT Id, Name, Description, IsActive, CreatedDate
                FROM Categories 
                ORDER BY Name";

            var dataTable = ExecuteQuery(sql);
            var categories = new List<Category>();

            foreach (DataRow row in dataTable.Rows)
            {
                categories.Add(MapRowToCategory(row));
            }

            return categories;
        }

        public List<Category> GetActiveCategories()
        {
            string sql = @"
                SELECT Id, Name, Description, IsActive, CreatedDate
                FROM Categories 
                WHERE IsActive = 1
                ORDER BY Name";

            var dataTable = ExecuteQuery(sql);
            var categories = new List<Category>();

            foreach (DataRow row in dataTable.Rows)
            {
                categories.Add(MapRowToCategory(row));
            }

            return categories;
        }

        public Category? GetCategoryById(int id)
        {
            string sql = @"
                SELECT Id, Name, Description, IsActive, CreatedDate
                FROM Categories 
                WHERE Id = @id";

            var parameters = new[] { CreateParameter("@id", id) };
            var dataTable = ExecuteQuery(sql, parameters);

            if (dataTable.Rows.Count == 0)
                return null;

            return MapRowToCategory(dataTable.Rows[0]);
        }

        public int AddCategory(Category category)
        {
            string sql = @"
                INSERT INTO Categories (Name, Description, IsActive)
                VALUES (@name, @description, @isActive);
                SELECT last_insert_rowid();";

            var parameters = new[]
            {
                CreateParameter("@name", category.Name),
                CreateParameter("@description", category.Description),
                CreateParameter("@isActive", category.IsActive)
            };

            return ExecuteScalar<int>(sql, parameters);
        }

        public bool UpdateCategory(Category category)
        {
            string sql = @"
                UPDATE Categories 
                SET Name = @name, Description = @description, IsActive = @isActive
                WHERE Id = @id";

            var parameters = new[]
            {
                CreateParameter("@id", category.Id),
                CreateParameter("@name", category.Name),
                CreateParameter("@description", category.Description),
                CreateParameter("@isActive", category.IsActive)
            };

            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public bool DeleteCategory(int id)
        {
            // Check if category has products
            string checkSql = "SELECT COUNT(*) FROM Products WHERE CategoryId = @id";
            var checkParams = new[] { CreateParameter("@id", id) };
            int productCount = ExecuteScalar<int>(checkSql, checkParams);

            if (productCount > 0)
                return false; // Cannot delete category with products

            string sql = "DELETE FROM Categories WHERE Id = @id";
            var parameters = new[] { CreateParameter("@id", id) };
            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public bool IsCategoryNameExists(string name, int? excludeId = null)
        {
            string sql = "SELECT COUNT(*) FROM Categories WHERE Name = @name";
            var parameters = new List<SQLiteParameter> { CreateParameter("@name", name) };

            if (excludeId.HasValue)
            {
                sql += " AND Id != @excludeId";
                parameters.Add(CreateParameter("@excludeId", excludeId.Value));
            }

            return ExecuteScalar<int>(sql, parameters.ToArray()) > 0;
        }

        private Category MapRowToCategory(DataRow row)
        {
            return new Category
            {
                Id = Convert.ToInt32(row["Id"]),
                Name = GetString(row["Name"]),
                Description = GetString(row["Description"]),
                IsActive = GetBoolean(row["IsActive"]),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"])
            };
        }
    }
}
