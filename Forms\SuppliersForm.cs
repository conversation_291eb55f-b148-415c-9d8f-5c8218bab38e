using SmartBooks.Models;
using SmartBooks.DAL;

namespace SmartBooks.Forms
{
    public partial class SuppliersForm : Form
    {
        private SupplierDAL _supplierDAL;
        private List<Supplier> _suppliers;
        private Supplier? _selectedSupplier;

        public SuppliersForm()
        {
            InitializeComponent();
            _supplierDAL = new SupplierDAL();
            _suppliers = new List<Supplier>();
        }

        private void SuppliersForm_Load(object sender, EventArgs e)
        {
            LoadSuppliers();
            SetupDataGridView();
            ClearForm();
        }

        private void SetupDataGridView()
        {
            dgvSuppliers.AutoGenerateColumns = false;
            dgvSuppliers.Columns.Clear();

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "الرقم",
                DataPropertyName = "Id",
                Width = 80,
                ReadOnly = true
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "الاسم",
                DataPropertyName = "Name",
                Width = 200
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "الهاتف",
                DataPropertyName = "Phone",
                Width = 120
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                DataPropertyName = "Email",
                Width = 150
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Balance",
                HeaderText = "الرصيد",
                DataPropertyName = "Balance",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            dgvSuppliers.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشط",
                DataPropertyName = "IsActive",
                Width = 60
            });
        }

        private void LoadSuppliers()
        {
            try
            {
                _suppliers = _supplierDAL.GetAllSuppliers();
                dgvSuppliers.DataSource = _suppliers;
                lblTotalSuppliers.Text = $"إجمالي الموردين: {_suppliers.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            ClearForm();
            _selectedSupplier = null;
            txtName.Focus();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                SaveSupplier();
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المورد", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPhone.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الهاتف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPhone.Focus();
                return false;
            }

            return true;
        }

        private void SaveSupplier()
        {
            try
            {
                var supplier = new Supplier
                {
                    Name = txtName.Text.Trim(),
                    Phone = txtPhone.Text.Trim(),
                    Email = txtEmail.Text.Trim(),
                    Address = txtAddress.Text.Trim(),
                    IsActive = chkIsActive.Checked
                };

                if (_selectedSupplier == null)
                {
                    // Add new supplier
                    int supplierId = _supplierDAL.AddSupplier(supplier);
                    if (supplierId > 0)
                    {
                        MessageBox.Show("تم إضافة المورد بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadSuppliers();
                        ClearForm();
                    }
                }
                else
                {
                    // Update existing supplier
                    supplier.Id = _selectedSupplier.Id;
                    if (_supplierDAL.UpdateSupplier(supplier))
                    {
                        MessageBox.Show("تم تحديث المورد بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadSuppliers();
                        ClearForm();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المورد: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvSuppliers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvSuppliers.SelectedRows[0];
                _selectedSupplier = (Supplier)selectedRow.DataBoundItem;
                LoadSupplierToForm(_selectedSupplier);
            }
            else
            {
                MessageBox.Show("يرجى اختيار مورد للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void LoadSupplierToForm(Supplier supplier)
        {
            txtName.Text = supplier.Name;
            txtPhone.Text = supplier.Phone;
            txtEmail.Text = supplier.Email;
            txtAddress.Text = supplier.Address;
            chkIsActive.Checked = supplier.IsActive;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvSuppliers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvSuppliers.SelectedRows[0];
                var supplier = (Supplier)selectedRow.DataBoundItem;

                if (MessageBox.Show($"هل تريد حذف المورد '{supplier.Name}'؟", "تأكيد الحذف", 
                                  MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    try
                    {
                        if (_supplierDAL.DeleteSupplier(supplier.Id))
                        {
                            MessageBox.Show("تم حذف المورد بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadSuppliers();
                            ClearForm();
                        }
                        else
                        {
                            MessageBox.Show("لا يمكن حذف المورد لوجود معاملات مرتبطة به", "خطأ", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار مورد للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void ClearForm()
        {
            txtName.Clear();
            txtPhone.Clear();
            txtEmail.Clear();
            txtAddress.Clear();
            chkIsActive.Checked = true;
            _selectedSupplier = null;
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                dgvSuppliers.DataSource = _suppliers;
            }
            else
            {
                var filteredSuppliers = _suppliers.Where(s => 
                    s.Name.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase) ||
                    s.Phone.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase) ||
                    (!string.IsNullOrEmpty(s.Email) && s.Email.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase))
                ).ToList();

                dgvSuppliers.DataSource = filteredSuppliers;
            }
        }

        private void dgvSuppliers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadSuppliers();
            txtSearch.Clear();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnViewHistory_Click(object sender, EventArgs e)
        {
            if (dgvSuppliers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvSuppliers.SelectedRows[0];
                var supplier = (Supplier)selectedRow.DataBoundItem;
                
                // Show supplier purchase history
                var historyForm = new SupplierHistoryForm(supplier);
                historyForm.ShowDialog();
            }
            else
            {
                MessageBox.Show("يرجى اختيار مورد لعرض تاريخه", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
    }
}
