using SmartBooks.Models;
using System.Data.SQLite;
using System.Data;

namespace SmartBooks.DAL
{
    public class UserDAL : BaseDAL
    {
        public User? ValidateUser(string username, string password)
        {
            string sql = @"
                SELECT Id, Username, Password, FullName, Role, IsActive, 
                       CanAccessSales, CanAccessPurchases, CanAccessInventory, 
                       CanAccessReports, CanAccessSettings, CreatedDate, LastLogin
                FROM Users 
                WHERE Username = @username AND Password = @password AND IsActive = 1";

            var parameters = new[]
            {
                CreateParameter("@username", username),
                CreateParameter("@password", password)
            };

            var dataTable = ExecuteQuery(sql, parameters);
            if (dataTable.Rows.Count == 0)
                return null;

            var row = dataTable.Rows[0];
            return MapRowToUser(row);
        }

        public List<User> GetAllUsers()
        {
            string sql = @"
                SELECT Id, Username, Password, FullName, Role, IsActive, 
                       CanAccessSales, CanAccessPurchases, CanAccessInventory, 
                       CanAccessReports, CanAccessSettings, CreatedDate, LastLogin
                FROM Users 
                ORDER BY FullName";

            var dataTable = ExecuteQuery(sql);
            var users = new List<User>();

            foreach (DataRow row in dataTable.Rows)
            {
                users.Add(MapRowToUser(row));
            }

            return users;
        }

        public User? GetUserById(int id)
        {
            string sql = @"
                SELECT Id, Username, Password, FullName, Role, IsActive, 
                       CanAccessSales, CanAccessPurchases, CanAccessInventory, 
                       CanAccessReports, CanAccessSettings, CreatedDate, LastLogin
                FROM Users 
                WHERE Id = @id";

            var parameters = new[] { CreateParameter("@id", id) };
            var dataTable = ExecuteQuery(sql, parameters);

            if (dataTable.Rows.Count == 0)
                return null;

            return MapRowToUser(dataTable.Rows[0]);
        }

        public int AddUser(User user)
        {
            string sql = @"
                INSERT INTO Users (Username, Password, FullName, Role, IsActive, 
                                 CanAccessSales, CanAccessPurchases, CanAccessInventory, 
                                 CanAccessReports, CanAccessSettings)
                VALUES (@username, @password, @fullName, @role, @isActive, 
                        @canAccessSales, @canAccessPurchases, @canAccessInventory, 
                        @canAccessReports, @canAccessSettings);
                SELECT last_insert_rowid();";

            var parameters = new[]
            {
                CreateParameter("@username", user.Username),
                CreateParameter("@password", user.Password),
                CreateParameter("@fullName", user.FullName),
                CreateParameter("@role", user.Role),
                CreateParameter("@isActive", user.IsActive),
                CreateParameter("@canAccessSales", user.CanAccessSales),
                CreateParameter("@canAccessPurchases", user.CanAccessPurchases),
                CreateParameter("@canAccessInventory", user.CanAccessInventory),
                CreateParameter("@canAccessReports", user.CanAccessReports),
                CreateParameter("@canAccessSettings", user.CanAccessSettings)
            };

            return ExecuteScalar<int>(sql, parameters);
        }

        public bool UpdateUser(User user)
        {
            string sql = @"
                UPDATE Users 
                SET Username = @username, Password = @password, FullName = @fullName, 
                    Role = @role, IsActive = @isActive, CanAccessSales = @canAccessSales, 
                    CanAccessPurchases = @canAccessPurchases, CanAccessInventory = @canAccessInventory, 
                    CanAccessReports = @canAccessReports, CanAccessSettings = @canAccessSettings
                WHERE Id = @id";

            var parameters = new[]
            {
                CreateParameter("@id", user.Id),
                CreateParameter("@username", user.Username),
                CreateParameter("@password", user.Password),
                CreateParameter("@fullName", user.FullName),
                CreateParameter("@role", user.Role),
                CreateParameter("@isActive", user.IsActive),
                CreateParameter("@canAccessSales", user.CanAccessSales),
                CreateParameter("@canAccessPurchases", user.CanAccessPurchases),
                CreateParameter("@canAccessInventory", user.CanAccessInventory),
                CreateParameter("@canAccessReports", user.CanAccessReports),
                CreateParameter("@canAccessSettings", user.CanAccessSettings)
            };

            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public bool DeleteUser(int id)
        {
            string sql = "DELETE FROM Users WHERE Id = @id";
            var parameters = new[] { CreateParameter("@id", id) };
            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public bool UpdateLastLogin(int userId)
        {
            string sql = "UPDATE Users SET LastLogin = @lastLogin WHERE Id = @id";
            var parameters = new[]
            {
                CreateParameter("@id", userId),
                CreateParameter("@lastLogin", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            };
            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public bool IsUsernameExists(string username, int? excludeId = null)
        {
            string sql = "SELECT COUNT(*) FROM Users WHERE Username = @username";
            var parameters = new List<SQLiteParameter> { CreateParameter("@username", username) };

            if (excludeId.HasValue)
            {
                sql += " AND Id != @excludeId";
                parameters.Add(CreateParameter("@excludeId", excludeId.Value));
            }

            return ExecuteScalar<int>(sql, parameters.ToArray()) > 0;
        }

        private User MapRowToUser(DataRow row)
        {
            return new User
            {
                Id = Convert.ToInt32(row["Id"]),
                Username = GetString(row["Username"]),
                Password = GetString(row["Password"]),
                FullName = GetString(row["FullName"]),
                Role = GetString(row["Role"]),
                IsActive = GetBoolean(row["IsActive"]),
                CanAccessSales = GetBoolean(row["CanAccessSales"]),
                CanAccessPurchases = GetBoolean(row["CanAccessPurchases"]),
                CanAccessInventory = GetBoolean(row["CanAccessInventory"]),
                CanAccessReports = GetBoolean(row["CanAccessReports"]),
                CanAccessSettings = GetBoolean(row["CanAccessSettings"]),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                LastLogin = GetNullableDateTime(row["LastLogin"])
            };
        }
    }
}
