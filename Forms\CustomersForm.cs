using SmartBooks.Models;
using SmartBooks.DAL;

namespace SmartBooks.Forms
{
    public partial class CustomersForm : Form
    {
        private CustomerDAL _customerDAL;
        private List<Customer> _customers;
        private Customer? _selectedCustomer;

        public CustomersForm()
        {
            InitializeComponent();
            _customerDAL = new CustomerDAL();
            _customers = new List<Customer>();
        }

        private void CustomersForm_Load(object sender, EventArgs e)
        {
            LoadCustomers();
            SetupDataGridView();
            ClearForm();
        }

        private void SetupDataGridView()
        {
            dgvCustomers.AutoGenerateColumns = false;
            dgvCustomers.Columns.Clear();

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "الرقم",
                DataPropertyName = "Id",
                Width = 80,
                ReadOnly = true
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "الاسم",
                DataPropertyName = "Name",
                Width = 200
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "الهاتف",
                DataPropertyName = "Phone",
                Width = 120
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                DataPropertyName = "Email",
                Width = 150
            });

            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Balance",
                HeaderText = "الرصيد",
                DataPropertyName = "Balance",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            dgvCustomers.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشط",
                DataPropertyName = "IsActive",
                Width = 60
            });
        }

        private void LoadCustomers()
        {
            try
            {
                _customers = _customerDAL.GetAllCustomers();
                dgvCustomers.DataSource = _customers;
                lblTotalCustomers.Text = $"إجمالي العملاء: {_customers.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            ClearForm();
            _selectedCustomer = null;
            txtName.Focus();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                SaveCustomer();
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPhone.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الهاتف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPhone.Focus();
                return false;
            }

            return true;
        }

        private void SaveCustomer()
        {
            try
            {
                var customer = new Customer
                {
                    Name = txtName.Text.Trim(),
                    Phone = txtPhone.Text.Trim(),
                    Email = txtEmail.Text.Trim(),
                    Address = txtAddress.Text.Trim(),
                    IsActive = chkIsActive.Checked
                };

                if (_selectedCustomer == null)
                {
                    // Add new customer
                    int customerId = _customerDAL.AddCustomer(customer);
                    if (customerId > 0)
                    {
                        MessageBox.Show("تم إضافة العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadCustomers();
                        ClearForm();
                    }
                }
                else
                {
                    // Update existing customer
                    customer.Id = _selectedCustomer.Id;
                    if (_customerDAL.UpdateCustomer(customer))
                    {
                        MessageBox.Show("تم تحديث العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadCustomers();
                        ClearForm();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ العميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvCustomers.SelectedRows[0];
                _selectedCustomer = (Customer)selectedRow.DataBoundItem;
                LoadCustomerToForm(_selectedCustomer);
            }
            else
            {
                MessageBox.Show("يرجى اختيار عميل للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void LoadCustomerToForm(Customer customer)
        {
            txtName.Text = customer.Name;
            txtPhone.Text = customer.Phone;
            txtEmail.Text = customer.Email;
            txtAddress.Text = customer.Address;
            chkIsActive.Checked = customer.IsActive;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvCustomers.SelectedRows[0];
                var customer = (Customer)selectedRow.DataBoundItem;

                if (MessageBox.Show($"هل تريد حذف العميل '{customer.Name}'؟", "تأكيد الحذف", 
                                  MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    try
                    {
                        if (_customerDAL.DeleteCustomer(customer.Id))
                        {
                            MessageBox.Show("تم حذف العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadCustomers();
                            ClearForm();
                        }
                        else
                        {
                            MessageBox.Show("لا يمكن حذف العميل لوجود معاملات مرتبطة به", "خطأ", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار عميل للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void ClearForm()
        {
            txtName.Clear();
            txtPhone.Clear();
            txtEmail.Clear();
            txtAddress.Clear();
            chkIsActive.Checked = true;
            _selectedCustomer = null;
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                dgvCustomers.DataSource = _customers;
            }
            else
            {
                var filteredCustomers = _customers.Where(c => 
                    c.Name.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase) ||
                    c.Phone.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase) ||
                    (!string.IsNullOrEmpty(c.Email) && c.Email.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase))
                ).ToList();

                dgvCustomers.DataSource = filteredCustomers;
            }
        }

        private void dgvCustomers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadCustomers();
            txtSearch.Clear();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnViewHistory_Click(object sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvCustomers.SelectedRows[0];
                var customer = (Customer)selectedRow.DataBoundItem;

                // Show customer sales history
                var historyForm = new CustomerHistoryForm(customer);
                historyForm.ShowDialog();
            }
            else
            {
                MessageBox.Show("يرجى اختيار عميل لعرض تاريخه", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
    }
}
