using System.Data.SQLite;
using Microsoft.Extensions.Configuration;

namespace SmartBooks.DAL
{
    public static class DatabaseHelper
    {
        private static string? _connectionString;

        public static string ConnectionString
        {
            get
            {
                if (string.IsNullOrEmpty(_connectionString))
                {
                    var configuration = new ConfigurationBuilder()
                        .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                        .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                        .Build();

                    _connectionString = configuration.GetConnectionString("DefaultConnection");
                }
                return _connectionString ?? "Data Source=SmartBooks.db;Version=3;";
            }
        }

        public static void InitializeDatabase()
        {
            try
            {
                using var connection = new SQLiteConnection(ConnectionString);
                connection.Open();

                // Create all tables
                CreateUsersTable(connection);
                CreateCustomersTable(connection);
                CreateSuppliersTable(connection);
                CreateCategoriesTable(connection);
                CreateProductsTable(connection);
                CreateProductBarcodesTable(connection);
                CreateCashBoxesTable(connection);
                CreateExpenseTypesTable(connection);
                CreateExpensesTable(connection);
                CreateSalesInvoicesTable(connection);
                CreateSalesInvoiceItemsTable(connection);
                CreatePurchaseInvoicesTable(connection);
                CreatePurchaseInvoiceItemsTable(connection);
                CreateSalesReturnsTable(connection);
                CreateSalesReturnItemsTable(connection);
                CreatePurchaseReturnsTable(connection);
                CreatePurchaseReturnItemsTable(connection);
                CreateCashBoxTransactionsTable(connection);
                CreateCustomerPaymentsTable(connection);
                CreateSupplierPaymentsTable(connection);
                CreateInventoryMovementsTable(connection);

                // Insert default data
                InsertDefaultData(connection);

                connection.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void CreateUsersTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username TEXT NOT NULL UNIQUE,
                    Password TEXT NOT NULL,
                    FullName TEXT NOT NULL,
                    Role TEXT NOT NULL DEFAULT 'User',
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CanAccessSales INTEGER NOT NULL DEFAULT 1,
                    CanAccessPurchases INTEGER NOT NULL DEFAULT 1,
                    CanAccessInventory INTEGER NOT NULL DEFAULT 1,
                    CanAccessReports INTEGER NOT NULL DEFAULT 1,
                    CanAccessSettings INTEGER NOT NULL DEFAULT 0,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastLogin TEXT
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateCustomersTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS Customers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Phone TEXT,
                    Email TEXT,
                    Address TEXT,
                    TaxNumber TEXT,
                    CreditLimit REAL NOT NULL DEFAULT 0,
                    CurrentBalance REAL NOT NULL DEFAULT 0,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    Notes TEXT
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateSuppliersTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS Suppliers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Phone TEXT,
                    Email TEXT,
                    Address TEXT,
                    TaxNumber TEXT,
                    CurrentBalance REAL NOT NULL DEFAULT 0,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    Notes TEXT
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateCategoriesTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS Categories (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL UNIQUE,
                    Description TEXT,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateProductsTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS Products (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    CategoryId INTEGER,
                    Unit TEXT NOT NULL DEFAULT 'قطعة',
                    PurchasePrice REAL NOT NULL DEFAULT 0,
                    SalePrice REAL NOT NULL DEFAULT 0,
                    MinStock REAL NOT NULL DEFAULT 0,
                    CurrentStock REAL NOT NULL DEFAULT 0,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    Description TEXT,
                    FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateProductBarcodesTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS ProductBarcodes (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProductId INTEGER NOT NULL,
                    Barcode TEXT NOT NULL UNIQUE,
                    IsDefault INTEGER NOT NULL DEFAULT 0,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id) ON DELETE CASCADE
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateCashBoxesTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS CashBoxes (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL UNIQUE,
                    CurrentBalance REAL NOT NULL DEFAULT 0,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    Description TEXT
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateExpenseTypesTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS ExpenseTypes (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL UNIQUE,
                    Description TEXT,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateExpensesTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS Expenses (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ExpenseTypeId INTEGER NOT NULL,
                    CashBoxId INTEGER NOT NULL,
                    Amount REAL NOT NULL,
                    Description TEXT,
                    ExpenseDate TEXT NOT NULL,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UserId INTEGER,
                    FOREIGN KEY (ExpenseTypeId) REFERENCES ExpenseTypes(Id),
                    FOREIGN KEY (CashBoxId) REFERENCES CashBoxes(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateSalesInvoicesTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS SalesInvoices (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT NOT NULL UNIQUE,
                    CustomerId INTEGER,
                    InvoiceDate TEXT NOT NULL,
                    TotalAmount REAL NOT NULL DEFAULT 0,
                    DiscountAmount REAL NOT NULL DEFAULT 0,
                    TaxAmount REAL NOT NULL DEFAULT 0,
                    NetAmount REAL NOT NULL DEFAULT 0,
                    PaidAmount REAL NOT NULL DEFAULT 0,
                    RemainingAmount REAL NOT NULL DEFAULT 0,
                    PaymentMethod TEXT NOT NULL DEFAULT 'نقدي',
                    CashBoxId INTEGER,
                    Notes TEXT,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UserId INTEGER,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                    FOREIGN KEY (CashBoxId) REFERENCES CashBoxes(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateSalesInvoiceItemsTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS SalesInvoiceItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    Quantity REAL NOT NULL,
                    UnitPrice REAL NOT NULL,
                    TotalPrice REAL NOT NULL,
                    FOREIGN KEY (InvoiceId) REFERENCES SalesInvoices(Id) ON DELETE CASCADE,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreatePurchaseInvoicesTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS PurchaseInvoices (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT NOT NULL UNIQUE,
                    SupplierId INTEGER NOT NULL,
                    InvoiceDate TEXT NOT NULL,
                    TotalAmount REAL NOT NULL DEFAULT 0,
                    DiscountAmount REAL NOT NULL DEFAULT 0,
                    TaxAmount REAL NOT NULL DEFAULT 0,
                    NetAmount REAL NOT NULL DEFAULT 0,
                    PaidAmount REAL NOT NULL DEFAULT 0,
                    RemainingAmount REAL NOT NULL DEFAULT 0,
                    PaymentMethod TEXT NOT NULL DEFAULT 'نقدي',
                    CashBoxId INTEGER,
                    Notes TEXT,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UserId INTEGER,
                    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id),
                    FOREIGN KEY (CashBoxId) REFERENCES CashBoxes(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreatePurchaseInvoiceItemsTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS PurchaseInvoiceItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    Quantity REAL NOT NULL,
                    UnitPrice REAL NOT NULL,
                    TotalPrice REAL NOT NULL,
                    FOREIGN KEY (InvoiceId) REFERENCES PurchaseInvoices(Id) ON DELETE CASCADE,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateSalesReturnsTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS SalesReturns (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ReturnNumber TEXT NOT NULL UNIQUE,
                    OriginalInvoiceId INTEGER,
                    CustomerId INTEGER,
                    ReturnDate TEXT NOT NULL,
                    TotalAmount REAL NOT NULL DEFAULT 0,
                    CashBoxId INTEGER,
                    Notes TEXT,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UserId INTEGER,
                    FOREIGN KEY (OriginalInvoiceId) REFERENCES SalesInvoices(Id),
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                    FOREIGN KEY (CashBoxId) REFERENCES CashBoxes(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateSalesReturnItemsTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS SalesReturnItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ReturnId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    Quantity REAL NOT NULL,
                    UnitPrice REAL NOT NULL,
                    TotalPrice REAL NOT NULL,
                    FOREIGN KEY (ReturnId) REFERENCES SalesReturns(Id) ON DELETE CASCADE,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreatePurchaseReturnsTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS PurchaseReturns (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ReturnNumber TEXT NOT NULL UNIQUE,
                    OriginalInvoiceId INTEGER,
                    SupplierId INTEGER NOT NULL,
                    ReturnDate TEXT NOT NULL,
                    TotalAmount REAL NOT NULL DEFAULT 0,
                    CashBoxId INTEGER,
                    Notes TEXT,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UserId INTEGER,
                    FOREIGN KEY (OriginalInvoiceId) REFERENCES PurchaseInvoices(Id),
                    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id),
                    FOREIGN KEY (CashBoxId) REFERENCES CashBoxes(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreatePurchaseReturnItemsTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS PurchaseReturnItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ReturnId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    Quantity REAL NOT NULL,
                    UnitPrice REAL NOT NULL,
                    TotalPrice REAL NOT NULL,
                    FOREIGN KEY (ReturnId) REFERENCES PurchaseReturns(Id) ON DELETE CASCADE,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateCashBoxTransactionsTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS CashBoxTransactions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CashBoxId INTEGER NOT NULL,
                    TransactionType TEXT NOT NULL, -- 'إيداع', 'سحب', 'تحويل'
                    Amount REAL NOT NULL,
                    Description TEXT,
                    ReferenceType TEXT, -- 'مبيعات', 'مشتريات', 'مصروفات', 'دفع عميل', 'دفع مورد', 'يدوي'
                    ReferenceId INTEGER,
                    ToCashBoxId INTEGER, -- للتحويلات
                    TransactionDate TEXT NOT NULL,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UserId INTEGER,
                    FOREIGN KEY (CashBoxId) REFERENCES CashBoxes(Id),
                    FOREIGN KEY (ToCashBoxId) REFERENCES CashBoxes(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateCustomerPaymentsTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS CustomerPayments (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CustomerId INTEGER NOT NULL,
                    Amount REAL NOT NULL,
                    PaymentDate TEXT NOT NULL,
                    PaymentMethod TEXT NOT NULL DEFAULT 'نقدي',
                    CashBoxId INTEGER,
                    Notes TEXT,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UserId INTEGER,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                    FOREIGN KEY (CashBoxId) REFERENCES CashBoxes(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateSupplierPaymentsTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS SupplierPayments (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    SupplierId INTEGER NOT NULL,
                    Amount REAL NOT NULL,
                    PaymentDate TEXT NOT NULL,
                    PaymentMethod TEXT NOT NULL DEFAULT 'نقدي',
                    CashBoxId INTEGER,
                    Notes TEXT,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UserId INTEGER,
                    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id),
                    FOREIGN KEY (CashBoxId) REFERENCES CashBoxes(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void CreateInventoryMovementsTable(SQLiteConnection connection)
        {
            string sql = @"
                CREATE TABLE IF NOT EXISTS InventoryMovements (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProductId INTEGER NOT NULL,
                    MovementType TEXT NOT NULL, -- 'دخول', 'خروج', 'تعديل'
                    Quantity REAL NOT NULL,
                    ReferenceType TEXT, -- 'مبيعات', 'مشتريات', 'مرتجع مبيعات', 'مرتجع مشتريات', 'تعديل يدوي'
                    ReferenceId INTEGER,
                    MovementDate TEXT NOT NULL,
                    Notes TEXT,
                    CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UserId INTEGER,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                );";
            ExecuteNonQuery(connection, sql);
        }

        private static void InsertDefaultData(SQLiteConnection connection)
        {
            // Insert default admin user
            string checkAdmin = "SELECT COUNT(*) FROM Users WHERE Username = 'admin'";
            using var checkCommand = new SQLiteCommand(checkAdmin, connection);
            int adminCount = Convert.ToInt32(checkCommand.ExecuteScalar());

            if (adminCount == 0)
            {
                string insertAdmin = @"
                    INSERT INTO Users (Username, Password, FullName, Role, CanAccessSettings)
                    VALUES ('admin', 'admin123', 'المدير العام', 'Admin', 1)";
                ExecuteNonQuery(connection, insertAdmin);
            }

            // Insert default cash box
            string checkCashBox = "SELECT COUNT(*) FROM CashBoxes WHERE Name = 'الخزينة الرئيسية'";
            using var checkCashCommand = new SQLiteCommand(checkCashBox, connection);
            int cashBoxCount = Convert.ToInt32(checkCashCommand.ExecuteScalar());

            if (cashBoxCount == 0)
            {
                string insertCashBox = @"
                    INSERT INTO CashBoxes (Name, Description)
                    VALUES ('الخزينة الرئيسية', 'الخزينة الافتراضية للنظام')";
                ExecuteNonQuery(connection, insertCashBox);
            }

            // Insert default category
            string checkCategory = "SELECT COUNT(*) FROM Categories WHERE Name = 'عام'";
            using var checkCatCommand = new SQLiteCommand(checkCategory, connection);
            int categoryCount = Convert.ToInt32(checkCatCommand.ExecuteScalar());

            if (categoryCount == 0)
            {
                string insertCategory = @"
                    INSERT INTO Categories (Name, Description)
                    VALUES ('عام', 'تصنيف افتراضي للمنتجات')";
                ExecuteNonQuery(connection, insertCategory);
            }

            // Insert default expense types
            string checkExpenseTypes = "SELECT COUNT(*) FROM ExpenseTypes";
            using var checkExpCommand = new SQLiteCommand(checkExpenseTypes, connection);
            int expenseTypesCount = Convert.ToInt32(checkExpCommand.ExecuteScalar());

            if (expenseTypesCount == 0)
            {
                string[] expenseTypes = { "مصروفات إدارية", "مصروفات تشغيلية", "رواتب", "إيجار", "كهرباء", "مياه", "صيانة", "وقود", "أخرى" };
                foreach (string expenseType in expenseTypes)
                {
                    string insertExpenseType = $"INSERT INTO ExpenseTypes (Name) VALUES ('{expenseType}')";
                    ExecuteNonQuery(connection, insertExpenseType);
                }
            }
        }

        private static void ExecuteNonQuery(SQLiteConnection connection, string sql)
        {
            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }
    }
}
