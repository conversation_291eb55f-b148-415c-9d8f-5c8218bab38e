using SmartBooks.Models;
using System.Data.SQLite;
using System.Data;

namespace SmartBooks.DAL
{
    public class ProductDAL : BaseDAL
    {
        public List<Product> GetAllProducts()
        {
            string sql = @"
                SELECT p.Id, p.Name, p.CategoryId, p.Unit, p.PurchasePrice, p.SalePrice, 
                       p.MinStock, p.CurrentStock, p.IsActive, p.CreatedDate, p.Description,
                       c.Name as CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                ORDER BY p.Name";

            var dataTable = ExecuteQuery(sql);
            var products = new List<Product>();

            foreach (DataRow row in dataTable.Rows)
            {
                var product = MapRowToProduct(row);
                product.Barcodes = GetProductBarcodes(product.Id);
                products.Add(product);
            }

            return products;
        }

        public List<Product> GetActiveProducts()
        {
            string sql = @"
                SELECT p.Id, p.Name, p.CategoryId, p.Unit, p.PurchasePrice, p.SalePrice, 
                       p.Min<PERSON>tock, p.CurrentStock, p.IsActive, p.CreatedDate, p.Description,
                       c.Name as CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.IsActive = 1
                ORDER BY p.Name";

            var dataTable = ExecuteQuery(sql);
            var products = new List<Product>();

            foreach (DataRow row in dataTable.Rows)
            {
                var product = MapRowToProduct(row);
                product.Barcodes = GetProductBarcodes(product.Id);
                products.Add(product);
            }

            return products;
        }

        public Product? GetProductById(int id)
        {
            string sql = @"
                SELECT p.Id, p.Name, p.CategoryId, p.Unit, p.PurchasePrice, p.SalePrice, 
                       p.MinStock, p.CurrentStock, p.IsActive, p.CreatedDate, p.Description,
                       c.Name as CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.Id = @id";

            var parameters = new[] { CreateParameter("@id", id) };
            var dataTable = ExecuteQuery(sql, parameters);

            if (dataTable.Rows.Count == 0)
                return null;

            var product = MapRowToProduct(dataTable.Rows[0]);
            product.Barcodes = GetProductBarcodes(product.Id);
            return product;
        }

        public Product? GetProductByBarcode(string barcode)
        {
            string sql = @"
                SELECT p.Id, p.Name, p.CategoryId, p.Unit, p.PurchasePrice, p.SalePrice, 
                       p.MinStock, p.CurrentStock, p.IsActive, p.CreatedDate, p.Description,
                       c.Name as CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                INNER JOIN ProductBarcodes pb ON p.Id = pb.ProductId
                WHERE pb.Barcode = @barcode AND p.IsActive = 1";

            var parameters = new[] { CreateParameter("@barcode", barcode) };
            var dataTable = ExecuteQuery(sql, parameters);

            if (dataTable.Rows.Count == 0)
                return null;

            var product = MapRowToProduct(dataTable.Rows[0]);
            product.Barcodes = GetProductBarcodes(product.Id);
            return product;
        }

        public int AddProduct(Product product)
        {
            string sql = @"
                INSERT INTO Products (Name, CategoryId, Unit, PurchasePrice, SalePrice, 
                                    MinStock, CurrentStock, IsActive, Description)
                VALUES (@name, @categoryId, @unit, @purchasePrice, @salePrice, 
                        @minStock, @currentStock, @isActive, @description);
                SELECT last_insert_rowid();";

            var parameters = new[]
            {
                CreateParameter("@name", product.Name),
                CreateParameter("@categoryId", product.CategoryId),
                CreateParameter("@unit", product.Unit),
                CreateParameter("@purchasePrice", product.PurchasePrice),
                CreateParameter("@salePrice", product.SalePrice),
                CreateParameter("@minStock", product.MinStock),
                CreateParameter("@currentStock", product.CurrentStock),
                CreateParameter("@isActive", product.IsActive),
                CreateParameter("@description", product.Description)
            };

            int productId = ExecuteScalar<int>(sql, parameters);

            // Add barcodes
            foreach (var barcode in product.Barcodes)
            {
                AddProductBarcode(productId, barcode.Barcode, barcode.IsDefault);
            }

            return productId;
        }

        public bool UpdateProduct(Product product)
        {
            string sql = @"
                UPDATE Products 
                SET Name = @name, CategoryId = @categoryId, Unit = @unit, 
                    PurchasePrice = @purchasePrice, SalePrice = @salePrice, 
                    MinStock = @minStock, CurrentStock = @currentStock, 
                    IsActive = @isActive, Description = @description
                WHERE Id = @id";

            var parameters = new[]
            {
                CreateParameter("@id", product.Id),
                CreateParameter("@name", product.Name),
                CreateParameter("@categoryId", product.CategoryId),
                CreateParameter("@unit", product.Unit),
                CreateParameter("@purchasePrice", product.PurchasePrice),
                CreateParameter("@salePrice", product.SalePrice),
                CreateParameter("@minStock", product.MinStock),
                CreateParameter("@currentStock", product.CurrentStock),
                CreateParameter("@isActive", product.IsActive),
                CreateParameter("@description", product.Description)
            };

            bool result = ExecuteNonQuery(sql, parameters) > 0;

            if (result)
            {
                // Update barcodes
                DeleteProductBarcodes(product.Id);
                foreach (var barcode in product.Barcodes)
                {
                    AddProductBarcode(product.Id, barcode.Barcode, barcode.IsDefault);
                }
            }

            return result;
        }

        public bool DeleteProduct(int id)
        {
            // Delete barcodes first
            DeleteProductBarcodes(id);

            string sql = "DELETE FROM Products WHERE Id = @id";
            var parameters = new[] { CreateParameter("@id", id) };
            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public bool UpdateProductStock(int productId, decimal newStock)
        {
            string sql = "UPDATE Products SET CurrentStock = @stock WHERE Id = @id";
            var parameters = new[]
            {
                CreateParameter("@id", productId),
                CreateParameter("@stock", newStock)
            };
            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public bool AdjustProductStock(int productId, decimal adjustment)
        {
            string sql = "UPDATE Products SET CurrentStock = CurrentStock + @adjustment WHERE Id = @id";
            var parameters = new[]
            {
                CreateParameter("@id", productId),
                CreateParameter("@adjustment", adjustment)
            };
            return ExecuteNonQuery(sql, parameters) > 0;
        }

        public List<Product> SearchProducts(string searchTerm)
        {
            string sql = @"
                SELECT DISTINCT p.Id, p.Name, p.CategoryId, p.Unit, p.PurchasePrice, p.SalePrice, 
                       p.MinStock, p.CurrentStock, p.IsActive, p.CreatedDate, p.Description,
                       c.Name as CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                LEFT JOIN ProductBarcodes pb ON p.Id = pb.ProductId
                WHERE p.Name LIKE @searchTerm OR p.Description LIKE @searchTerm OR pb.Barcode LIKE @searchTerm
                ORDER BY p.Name";

            var parameters = new[] { CreateParameter("@searchTerm", $"%{searchTerm}%") };
            var dataTable = ExecuteQuery(sql, parameters);
            var products = new List<Product>();

            foreach (DataRow row in dataTable.Rows)
            {
                var product = MapRowToProduct(row);
                product.Barcodes = GetProductBarcodes(product.Id);
                products.Add(product);
            }

            return products;
        }

        public List<Product> GetLowStockProducts()
        {
            string sql = @"
                SELECT p.Id, p.Name, p.CategoryId, p.Unit, p.PurchasePrice, p.SalePrice, 
                       p.MinStock, p.CurrentStock, p.IsActive, p.CreatedDate, p.Description,
                       c.Name as CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.CurrentStock <= p.MinStock AND p.IsActive = 1
                ORDER BY p.Name";

            var dataTable = ExecuteQuery(sql);
            var products = new List<Product>();

            foreach (DataRow row in dataTable.Rows)
            {
                var product = MapRowToProduct(row);
                product.Barcodes = GetProductBarcodes(product.Id);
                products.Add(product);
            }

            return products;
        }

        private List<ProductBarcode> GetProductBarcodes(int productId)
        {
            string sql = @"
                SELECT Id, ProductId, Barcode, IsDefault, CreatedDate
                FROM ProductBarcodes 
                WHERE ProductId = @productId
                ORDER BY IsDefault DESC, CreatedDate";

            var parameters = new[] { CreateParameter("@productId", productId) };
            var dataTable = ExecuteQuery(sql, parameters);
            var barcodes = new List<ProductBarcode>();

            foreach (DataRow row in dataTable.Rows)
            {
                barcodes.Add(new ProductBarcode
                {
                    Id = Convert.ToInt32(row["Id"]),
                    ProductId = Convert.ToInt32(row["ProductId"]),
                    Barcode = GetString(row["Barcode"]),
                    IsDefault = GetBoolean(row["IsDefault"]),
                    CreatedDate = Convert.ToDateTime(row["CreatedDate"])
                });
            }

            return barcodes;
        }

        private bool AddProductBarcode(int productId, string barcode, bool isDefault)
        {
            // If this is default, make others non-default
            if (isDefault)
            {
                string updateSql = "UPDATE ProductBarcodes SET IsDefault = 0 WHERE ProductId = @productId";
                var updateParams = new[] { CreateParameter("@productId", productId) };
                ExecuteNonQuery(updateSql, updateParams);
            }

            string sql = @"
                INSERT INTO ProductBarcodes (ProductId, Barcode, IsDefault)
                VALUES (@productId, @barcode, @isDefault)";

            var parameters = new[]
            {
                CreateParameter("@productId", productId),
                CreateParameter("@barcode", barcode),
                CreateParameter("@isDefault", isDefault)
            };

            return ExecuteNonQuery(sql, parameters) > 0;
        }

        private bool DeleteProductBarcodes(int productId)
        {
            string sql = "DELETE FROM ProductBarcodes WHERE ProductId = @productId";
            var parameters = new[] { CreateParameter("@productId", productId) };
            return ExecuteNonQuery(sql, parameters) >= 0;
        }

        private Product MapRowToProduct(DataRow row)
        {
            var product = new Product
            {
                Id = Convert.ToInt32(row["Id"]),
                Name = GetString(row["Name"]),
                CategoryId = GetNullableInt(row["CategoryId"]),
                Unit = GetString(row["Unit"]),
                PurchasePrice = GetDecimal(row["PurchasePrice"]),
                SalePrice = GetDecimal(row["SalePrice"]),
                MinStock = GetDecimal(row["MinStock"]),
                CurrentStock = GetDecimal(row["CurrentStock"]),
                IsActive = GetBoolean(row["IsActive"]),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                Description = GetString(row["Description"])
            };

            if (product.CategoryId.HasValue && row["CategoryName"] != DBNull.Value)
            {
                product.Category = new Category
                {
                    Id = product.CategoryId.Value,
                    Name = GetString(row["CategoryName"])
                };
            }

            return product;
        }
    }
}
